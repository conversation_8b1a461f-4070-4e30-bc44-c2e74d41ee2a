import logging

from rtl2gds import Chip, flow
from rtl2gds.global_configs import R2G_BASE_DIR, StepName


def main():
    logging.basicConfig(
        format="[%(asctime)s - %(levelname)s - %(name)s]: %(message)s",
        level=logging.INFO,
        force=True,
    )
    logging.info("Start synthesis")
    test_design = Chip(
        config_dict={
            "RTL_FILE": f"{R2G_BASE_DIR}/design_zoo/gcd/gcd.v",
            "TOP_NAME": "gcd",
            "RESULT_DIR": f"{R2G_BASE_DIR}/tmp/test_synth_result",
            "FLATTEN_DESIGN": True,
            "CLK_FREQ_MHZ": 200,
            "CLK_PORT_NAME": "clk",
        }
    )
    flow.single_step.run(chip=test_design, expect_step=StepName.SYNTHESIS)
    logging.info("Synthesis finished")


if __name__ == "__main__":
    main()
