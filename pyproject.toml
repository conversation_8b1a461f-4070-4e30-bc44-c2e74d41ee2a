[project]
name = "rtl2gds_demo"
version = "0.0.1"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
description = "A tool to compile your RTL files into GDSII layouts."
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "License :: OSI Approved :: MIT License",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.10",
]
dependencies = [
    "pyyaml",
    "orjson",
    "klayout",
    "IPython",
]

[project.optional-dependencies]
cloud = [
    "fastapi[standard]",
]

[tool.pylint]
extension-pkg-whitelist = ["orjson"]

[project.urls]
Homepage = "https://atomgit.com/harrywh/rtl2gds"
Issues = "https://atomgit.com/harrywh/rtl2gds/issues"
Documentation = "https://atomgit.com/harrywh/rtl2gds"
Repository = "https://atomgit.com/harrywh/rtl2gds"

[tool.uv]
index-url = "https://mirrors.aliyun.com/pypi/simple/"

[tool.black]
line-length = 100
target-version = ['py310', 'py312']
include = '\.pyi?$'
exclude = '''
/(
    \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | bin
    | _build
    | buck-out
    | build
    | dist
    | foundry
    | demo
    | docs
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_gitignore = true
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
known_first_party = ["rtl2gds"]
skip = ["venv", "bin", "build", "dist", "third_party"]
