# place_instance -inst_name $INST_NAME -llx $INST_LLX  -lly $INST_LLY  -orient N  -cellmaster $CELL_MASTER

place_instance -inst_name gcd_inst_0 -llx 0  -lly 0  -orient N  -cellmaster gcd

place_instance -inst_name gcd_inst_1 -llx 0  -lly 1000  -orient N  -cellmaster gcd

place_instance -inst_name gcd_inst_2 -llx 1000  -lly 0  -orient N  -cellmaster gcd

place_instance -inst_name gcd_inst_3 -llx 1000  -lly 1000  -orient N  -cellmaster gcd

