/*
 *  yosys -- Yosys Open SYnthesis Suite
 *
 *  Copyright (C) 2021  Cologne Chip AG <<EMAIL>>
 *
 *  Permission to use, copy, modify, and/or distribute this software for any
 *  purpose with or without fee is hereby granted, provided that the above
 *  copyright notice and this permission notice appear in all copies.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 *  WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 *  MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 *  ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 *  WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 *  ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 *  OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 *
 */

(* blackbox *)
module CC_PLL #(
	parameter REF_CLK = "", // e.g. "10.0"
	parameter OUT_CLK = "", // e.g. "50.0"
	parameter PERF_MD = "", // LOWPOWER, ECONOMY, SPEED
	parameter LOCK_REQ = 1,
	parameter CLK270_DOUB = 0,
	parameter CLK180_DOUB = 0,
	parameter LOW_JITTER = 1,
	parameter CI_FILTER_CONST = 2,
	parameter CP_FILTER_CONST = 4
)(
	input  CLK_REF, CLK_FEEDBACK, USR_CLK_REF,
	input  USR_LOCKED_STDY_RST,
	output USR_PLL_LOCKED_STDY, USR_PLL_LOCKED,
	output CLK270, CLK180, CLK90, CLK0, CLK_REF_OUT
);
endmodule

(* blackbox *)
module CC_PLL_ADV #(
	parameter [95:0] PLL_CFG_A = 96'bx,
	parameter [95:0] PLL_CFG_B = 96'bx
)(
	input  CLK_REF, CLK_FEEDBACK, USR_CLK_REF,
	input  USR_LOCKED_STDY_RST, USR_SEL_A_B,
	output USR_PLL_LOCKED_STDY, USR_PLL_LOCKED,
	output CLK270, CLK180, CLK90, CLK0, CLK_REF_OUT
);
endmodule

(* blackbox *) (* keep *)
module CC_SERDES #(
	parameter  [4:0] RX_BUF_RESET_TIME = 3,
	parameter  [4:0] RX_PCS_RESET_TIME = 3,
	parameter  [4:0] RX_RESET_TIMER_PRESC = 0,
	parameter  [0:0] RX_RESET_DONE_GATE = 0,
	parameter  [4:0] RX_CDR_RESET_TIME = 3,
	parameter  [4:0] RX_EQA_RESET_TIME = 3,
	parameter  [4:0] RX_PMA_RESET_TIME = 3,
	parameter  [0:0] RX_WAIT_CDR_LOCK = 1,
	parameter  [0:0] RX_CALIB_EN = 0,
	parameter  [0:0] RX_CALIB_OVR = 0,
	parameter  [3:0] RX_CALIB_VAL = 0,
	parameter  [2:0] RX_RTERM_VCMSEL = 4,
	parameter  [0:0] RX_RTERM_PD = 0,
	parameter  [7:0] RX_EQA_CKP_LF = 8'hA3,
	parameter  [7:0] RX_EQA_CKP_HF = 8'hA3,
	parameter  [7:0] RX_EQA_CKP_OFFSET = 8'h01,
	parameter  [0:0] RX_EN_EQA = 0,
	parameter  [3:0] RX_EQA_LOCK_CFG = 0,
	parameter  [4:0] RX_TH_MON1 = 8,
	parameter  [3:0] RX_EN_EQA_EXT_VALUE = 0,
	parameter  [4:0] RX_TH_MON2 = 8,
	parameter  [4:0] RX_TAPW = 8,
	parameter  [4:0] RX_AFE_OFFSET = 8,
	parameter [15:0] RX_EQA_CONFIG = 16'h01C0,
	parameter  [4:0] RX_AFE_PEAK = 16,
	parameter  [3:0] RX_AFE_GAIN = 8,
	parameter  [2:0] RX_AFE_VCMSEL = 4,
	parameter  [7:0] RX_CDR_CKP = 8'hF8,
	parameter  [7:0] RX_CDR_CKI = 0,
	parameter  [8:0] RX_CDR_TRANS_TH = 128,
	parameter  [5:0] RX_CDR_LOCK_CFG = 8'h0B,
	parameter [14:0] RX_CDR_FREQ_ACC = 0,
	parameter [15:0] RX_CDR_PHASE_ACC = 0,
	parameter  [1:0] RX_CDR_SET_ACC_CONFIG = 0,
	parameter  [0:0] RX_CDR_FORCE_LOCK = 0,
	parameter  [9:0] RX_ALIGN_MCOMMA_VALUE = 10'h283,
	parameter  [0:0] RX_MCOMMA_ALIGN_OVR = 0,
	parameter  [0:0] RX_MCOMMA_ALIGN = 0,
	parameter  [9:0] RX_ALIGN_PCOMMA_VALUE = 10'h17C,
	parameter  [0:0] RX_PCOMMA_ALIGN_OVR = 0,
	parameter  [0:0] RX_PCOMMA_ALIGN = 0,
	parameter  [1:0] RX_ALIGN_COMMA_WORD = 0,
	parameter  [9:0] RX_ALIGN_COMMA_ENABLE = 10'h3FF,
	parameter  [1:0] RX_SLIDE_MODE = 0,
	parameter  [0:0] RX_COMMA_DETECT_EN_OVR = 0,
	parameter  [0:0] RX_COMMA_DETECT_EN = 0,
	parameter  [1:0] RX_SLIDE = 0,
	parameter  [0:0] RX_EYE_MEAS_EN = 0,
	parameter [14:0] RX_EYE_MEAS_CFG = 0,
	parameter  [5:0] RX_MON_PH_OFFSET = 0,
	parameter  [3:0] RX_EI_BIAS = 0,
	parameter  [3:0] RX_EI_BW_SEL = 4,
	parameter  [0:0] RX_EN_EI_DETECTOR_OVR = 0,
	parameter  [0:0] RX_EN_EI_DETECTOR = 0,
	parameter  [0:0] RX_DATA_SEL = 0,
	parameter  [0:0] RX_BUF_BYPASS = 0,
	parameter  [0:0] RX_CLKCOR_USE = 0,
	parameter  [5:0] RX_CLKCOR_MIN_LAT = 32,
	parameter  [5:0] RX_CLKCOR_MAX_LAT = 39,
	parameter  [9:0] RX_CLKCOR_SEQ_1_0 = 10'h1F7,
	parameter  [9:0] RX_CLKCOR_SEQ_1_1 = 10'h1F7,
	parameter  [9:0] RX_CLKCOR_SEQ_1_2 = 10'h1F7,
	parameter  [9:0] RX_CLKCOR_SEQ_1_3 = 10'h1F7,
	parameter  [0:0] RX_PMA_LOOPBACK = 0,
	parameter  [0:0] RX_PCS_LOOPBACK = 0,
	parameter  [1:0] RX_DATAPATH_SEL = 3,
	parameter  [0:0] RX_PRBS_OVR = 0,
	parameter  [2:0] RX_PRBS_SEL = 0,
	parameter  [0:0] RX_LOOPBACK_OVR = 0,
	parameter  [0:0] RX_PRBS_CNT_RESET = 0,
	parameter  [0:0] RX_POWER_DOWN_OVR = 0,
	parameter  [0:0] RX_POWER_DOWN_N = 0,
	parameter  [0:0] RX_RESET_OVR = 0,
	parameter  [0:0] RX_RESET = 0,
	parameter  [0:0] RX_PMA_RESET_OVR = 0,
	parameter  [0:0] RX_PMA_RESET = 0,
	parameter  [0:0] RX_EQA_RESET_OVR = 0,
	parameter  [0:0] RX_EQA_RESET = 0,
	parameter  [0:0] RX_CDR_RESET_OVR = 0,
	parameter  [0:0] RX_CDR_RESET = 0,
	parameter  [0:0] RX_PCS_RESET_OVR = 0,
	parameter  [0:0] RX_PCS_RESET = 0,
	parameter  [0:0] RX_BUF_RESET_OVR = 0,
	parameter  [0:0] RX_BUF_RESET = 0,
	parameter  [0:0] RX_POLARITY_OVR = 0,
	parameter  [0:0] RX_POLARITY = 0,
	parameter  [0:0] RX_8B10B_EN_OVR = 0,
	parameter  [0:0] RX_8B10B_EN = 0,
	parameter  [7:0] RX_8B10B_BYPASS = 0,
	parameter  [0:0] RX_BYTE_REALIGN = 0,
	parameter  [0:0] RX_DBG_EN = 0,
	parameter  [1:0] RX_DBG_SEL = 0,
	parameter  [0:0] RX_DBG_MODE = 0,
	parameter  [5:0] RX_DBG_SRAM_DELAY = 6'h05,
	parameter  [9:0] RX_DBG_ADDR = 0,
	parameter  [0:0] RX_DBG_RE = 0,
	parameter  [0:0] RX_DBG_WE = 0,
	parameter [19:0] RX_DBG_DATA = 0,
	parameter  [4:0] TX_SEL_PRE = 0,
	parameter  [4:0] TX_SEL_POST = 0,
	parameter  [4:0] TX_AMP = 15,
	parameter  [4:0] TX_BRANCH_EN_PRE = 0,
	parameter  [5:0] TX_BRANCH_EN_MAIN = 6'h3F,
	parameter  [4:0] TX_BRANCH_EN_POST = 0,
	parameter  [2:0] TX_TAIL_CASCODE = 4,
	parameter  [6:0] TX_DC_ENABLE = 63,
	parameter  [4:0] TX_DC_OFFSET = 0,
	parameter  [4:0] TX_CM_RAISE = 0,
	parameter  [4:0] TX_CM_THRESHOLD_0 = 14,
	parameter  [4:0] TX_CM_THRESHOLD_1 = 16,
	parameter  [4:0] TX_SEL_PRE_EI = 0,
	parameter  [4:0] TX_SEL_POST_EI = 0,
	parameter  [4:0] TX_AMP_EI = 15,
	parameter  [4:0] TX_BRANCH_EN_PRE_EI = 0,
	parameter  [5:0] TX_BRANCH_EN_MAIN_EI = 6'h3F,
	parameter  [4:0] TX_BRANCH_EN_POST_EI = 0,
	parameter  [2:0] TX_TAIL_CASCODE_EI = 4,
	parameter  [6:0] TX_DC_ENABLE_EI = 63,
	parameter  [4:0] TX_DC_OFFSET_EI = 0,
	parameter  [4:0] TX_CM_RAISE_EI = 0,
	parameter  [4:0] TX_CM_THRESHOLD_0_EI = 14,
	parameter  [4:0] TX_CM_THRESHOLD_1_EI = 16,
	parameter  [4:0] TX_SEL_PRE_RXDET = 0,
	parameter  [4:0] TX_SEL_POST_RXDET = 0,
	parameter  [4:0] TX_AMP_RXDET = 15,
	parameter  [4:0] TX_BRANCH_EN_PRE_RXDET = 0,
	parameter  [5:0] TX_BRANCH_EN_MAIN_RXDET = 6'h3F,
	parameter  [4:0] TX_BRANCH_EN_POST_RXDET = 0,
	parameter  [2:0] TX_TAIL_CASCODE_RXDET = 4,
	parameter  [6:0] TX_DC_ENABLE_RXDET = 63,
	parameter  [4:0] TX_DC_OFFSET_RXDET = 0,
	parameter  [4:0] TX_CM_RAISE_RXDET = 0,
	parameter  [4:0] TX_CM_THRESHOLD_0_RXDET = 14,
	parameter  [4:0] TX_CM_THRESHOLD_1_RXDET = 16,
	parameter  [0:0] TX_CALIB_EN = 0,
	parameter  [0:0] TX_CALIB_OVR = 0,
	parameter  [3:0] TX_CALIB_VAL = 0,
	parameter  [7:0] TX_CM_REG_KI = 8'h80,
	parameter  [0:0] TX_CM_SAR_EN = 0,
	parameter  [0:0] TX_CM_REG_EN = 1,
	parameter  [4:0] TX_PMA_RESET_TIME = 3,
	parameter  [4:0] TX_PCS_RESET_TIME = 3,
	parameter  [0:0] TX_PCS_RESET_OVR = 0,
	parameter  [0:0] TX_PCS_RESET = 0,
	parameter  [0:0] TX_PMA_RESET_OVR = 0,
	parameter  [0:0] TX_PMA_RESET = 0,
	parameter  [0:0] TX_RESET_OVR = 0,
	parameter  [0:0] TX_RESET = 0,
	parameter  [1:0] TX_PMA_LOOPBACK = 0,
	parameter  [0:0] TX_PCS_LOOPBACK = 0,
	parameter  [1:0] TX_DATAPATH_SEL = 3,
	parameter  [0:0] TX_PRBS_OVR = 0,
	parameter  [2:0] TX_PRBS_SEL = 0,
	parameter  [0:0] TX_PRBS_FORCE_ERR = 0,
	parameter  [0:0] TX_LOOPBACK_OVR = 0,
	parameter  [0:0] TX_POWER_DOWN_OVR = 0,
	parameter  [0:0] TX_POWER_DOWN_N = 0,
	parameter  [0:0] TX_ELEC_IDLE_OVR = 0,
	parameter  [0:0] TX_ELEC_IDLE = 0,
	parameter  [0:0] TX_DETECT_RX_OVR = 0,
	parameter  [0:0] TX_DETECT_RX = 0,
	parameter  [0:0] TX_POLARITY_OVR = 0,
	parameter  [0:0] TX_POLARITY = 0,
	parameter  [0:0] TX_8B10B_EN_OVR = 0,
	parameter  [0:0] TX_8B10B_EN = 0,
	parameter  [0:0] TX_DATA_OVR = 0,
	parameter  [2:0] TX_DATA_CNT = 0,
	parameter  [0:0] TX_DATA_VALID = 0,
	parameter  [0:0] PLL_EN_ADPLL_CTRL = 0,
	parameter  [0:0] PLL_CONFIG_SEL = 0,
	parameter  [0:0] PLL_SET_OP_LOCK = 0,
	parameter  [0:0] PLL_ENFORCE_LOCK = 0,
	parameter  [0:0] PLL_DISABLE_LOCK = 0,
	parameter  [0:0] PLL_LOCK_WINDOW = 1,
	parameter  [0:0] PLL_FAST_LOCK = 1,
	parameter  [0:0] PLL_SYNC_BYPASS = 0,
	parameter  [0:0] PLL_PFD_SELECT = 0,
	parameter  [0:0] PLL_REF_BYPASS = 0,
	parameter  [0:0] PLL_REF_SEL = 0,
	parameter  [0:0] PLL_REF_RTERM = 1,
	parameter  [5:0] PLL_FCNTRL = 58,
	parameter  [5:0] PLL_MAIN_DIVSEL = 27,
	parameter  [1:0] PLL_OUT_DIVSEL = 0,
	parameter  [4:0] PLL_CI = 3,
	parameter  [9:0] PLL_CP = 80,
	parameter  [3:0] PLL_AO = 0,
	parameter  [2:0] PLL_SCAP = 0,
	parameter  [1:0] PLL_FILTER_SHIFT = 2,
	parameter  [2:0] PLL_SAR_LIMIT = 2,
	parameter [10:0] PLL_FT = 512,
	parameter  [0:0] PLL_OPEN_LOOP = 0,
	parameter  [0:0] PLL_SCAP_AUTO_CAL = 1,
	parameter  [2:0] PLL_BISC_MODE = 4,
	parameter  [3:0] PLL_BISC_TIMER_MAX = 15,
	parameter  [0:0] PLL_BISC_OPT_DET_IND = 0,
	parameter  [0:0] PLL_BISC_PFD_SEL = 0,
	parameter  [0:0] PLL_BISC_DLY_DIR = 0,
	parameter  [2:0] PLL_BISC_COR_DLY = 1,
	parameter  [0:0] PLL_BISC_CAL_SIGN = 0,
	parameter  [0:0] PLL_BISC_CAL_AUTO = 1,
	parameter  [4:0] PLL_BISC_CP_MIN = 4,
	parameter  [4:0] PLL_BISC_CP_MAX = 18,
	parameter  [4:0] PLL_BISC_CP_START = 12,
	parameter  [4:0] PLL_BISC_DLY_PFD_MON_REF = 0,
	parameter  [4:0] PLL_BISC_DLY_PFD_MON_DIV = 2,
	parameter  [0:0] SERDES_ENABLE = 0,
	parameter  [0:0] SERDES_AUTO_INIT = 0,
	parameter  [0:0] SERDES_TESTMODE = 0
)(
	input [63:0] TX_DATA_I,
	input TX_RESET_I,
	input TX_PCS_RESET_I,
	input TX_PMA_RESET_I,
	input PLL_RESET_I,
	input TX_POWER_DOWN_N_I,
	input TX_POLARITY_I,
	input [2:0] TX_PRBS_SEL_I,
	input TX_PRBS_FORCE_ERR_I,
	input TX_8B10B_EN_I,
	input [7:0] TX_8B10B_BYPASS_I,
	input [7:0] TX_CHAR_IS_K_I,
	input [7:0] TX_CHAR_DISPMODE_I,
	input [7:0] TX_CHAR_DISPVAL_I,
	input TX_ELEC_IDLE_I,
	input TX_DETECT_RX_I,
	input [2:0] LOOPBACK_I,
	input TX_CLK_I,
	input RX_CLK_I,
	input RX_RESET_I,
	input RX_PMA_RESET_I,
	input RX_EQA_RESET_I,
	input RX_CDR_RESET_I,
	input RX_PCS_RESET_I,
	input RX_BUF_RESET_I,
	input RX_POWER_DOWN_N_I,
	input RX_POLARITY_I,
	input [2:0] RX_PRBS_SEL_I,
	input RX_PRBS_CNT_RESET_I,
	input RX_8B10B_EN_I,
	input [7:0] RX_8B10B_BYPASS_I,
	input RX_EN_EI_DETECTOR_I,
	input RX_COMMA_DETECT_EN_I,
	input RX_SLIDE_I,
	input RX_MCOMMA_ALIGN_I,
	input RX_PCOMMA_ALIGN_I,
	input REGFILE_CLK_I,
	input REGFILE_WE_I,
	input REGFILE_EN_I,
	input [7:0] REGFILE_ADDR_I,
	input [15:0] REGFILE_DI_I,
	input [15:0] REGFILE_MASK_I,
	output [63:0] RX_DATA_O,
	output [7:0] RX_NOT_IN_TABLE_O,
	output [7:0] RX_CHAR_IS_COMMA_O,
	output [7:0] RX_CHAR_IS_K_O,
	output [7:0] RX_DISP_ERR_O,
	output TX_DETECT_RX_DONE_O,
	output TX_DETECT_RX_PRESENT_O,
	output TX_BUF_ERR_O,
	output TX_RESET_DONE_O,
	output RX_PRBS_ERR_O,
	output RX_BUF_ERR_O,
	output RX_BYTE_IS_ALIGNED_O,
	output RX_BYTE_REALIGN_O,
	output RX_RESET_DONE_O,
	output RX_EI_EN_O,
	output RX_CLK_O,
	output PLL_CLK_O,
	output [15:0] REGFILE_DO_O,
	output REGFILE_RDY_O
);
endmodule

(* blackbox *) (* keep *)
module CC_CFG_CTRL(
	input [7:0] DATA,
	input CLK,
	input EN,
	input RECFG,
	input VALID
);
endmodule

(* blackbox *) (* keep *)
module CC_USR_RSTN (
	output USR_RSTN
);
endmodule
