/*
 *  yosys -- Yosys Open SYnthesis Suite
 *
 *  Copyright (C) 2012  <PERSON> <<EMAIL>>
 *
 *  Permission to use, copy, modify, and/or distribute this software for any
 *  purpose with or without fee is hereby granted, provided that the above
 *  copyright notice and this permission notice appear in all copies.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 *  WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 *  MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 *  ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 *  WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 *  ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 *  OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 *
 *  ---
 *
 *  The internal logic cell simulation library.
 *
 *  This Verilog library contains simple simulation models for the internal
 *  logic cells ($_NOT_ , $_AND_ , ...) that are generated by the default technology
 *  mapper (see "techmap.v" in this directory) and expected by the "abc" pass.
 *
 */

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_BUF_ (A, Y)
//* group comb_simple
//-
//- A buffer. This cell type is always optimized away by the opt_clean pass.
//-
//- Truth table:    A | Y
//-                ---+---
//-                 0 | 0
//-                 1 | 1
//-
module \$_BUF_ (A, Y);
input A;
output Y;
assign Y = A;
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_NOT_ (A, Y)
//* group comb_simple
//-
//- An inverter gate.
//-
//- Truth table:    A | Y
//-                ---+---
//-                 0 | 1
//-                 1 | 0
//-
module \$_NOT_ (A, Y);
input A;
output Y;
assign Y = ~A;
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_AND_ (A, B, Y)
//* group comb_simple
//-
//- A 2-input AND gate.
//-
//- Truth table:    A B | Y
//-                -----+---
//-                 0 0 | 0
//-                 0 1 | 0
//-                 1 0 | 0
//-                 1 1 | 1
//-
module \$_AND_ (A, B, Y);
input A, B;
output Y;
assign Y = A & B;
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_NAND_ (A, B, Y)
//* group comb_simple
//-
//- A 2-input NAND gate.
//-
//- Truth table:    A B | Y
//-                -----+---
//-                 0 0 | 1
//-                 0 1 | 1
//-                 1 0 | 1
//-                 1 1 | 0
//-
module \$_NAND_ (A, B, Y);
input A, B;
output Y;
assign Y = ~(A & B);
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_OR_ (A, B, Y)
//* group comb_simple
//-
//- A 2-input OR gate.
//-
//- Truth table:    A B | Y
//-                -----+---
//-                 0 0 | 0
//-                 0 1 | 1
//-                 1 0 | 1
//-                 1 1 | 1
//-
module \$_OR_ (A, B, Y);
input A, B;
output Y;
assign Y = A | B;
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_NOR_ (A, B, Y)
//* group comb_simple
//-
//- A 2-input NOR gate.
//-
//- Truth table:    A B | Y
//-                -----+---
//-                 0 0 | 1
//-                 0 1 | 0
//-                 1 0 | 0
//-                 1 1 | 0
//-
module \$_NOR_ (A, B, Y);
input A, B;
output Y;
assign Y = ~(A | B);
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_XOR_ (A, B, Y)
//* group comb_simple
//-
//- A 2-input XOR gate.
//-
//- Truth table:    A B | Y
//-                -----+---
//-                 0 0 | 0
//-                 0 1 | 1
//-                 1 0 | 1
//-                 1 1 | 0
//-
module \$_XOR_ (A, B, Y);
input A, B;
output Y;
assign Y = A ^ B;
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_XNOR_ (A, B, Y)
//* group comb_simple
//-
//- A 2-input XNOR gate.
//-
//- Truth table:    A B | Y
//-                -----+---
//-                 0 0 | 1
//-                 0 1 | 0
//-                 1 0 | 0
//-                 1 1 | 1
//-
module \$_XNOR_ (A, B, Y);
input A, B;
output Y;
assign Y = ~(A ^ B);
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ANDNOT_ (A, B, Y)
//* group comb_combined
//-
//- A 2-input AND-NOT gate.
//-
//- Truth table:    A B | Y
//-                -----+---
//-                 0 0 | 0
//-                 0 1 | 0
//-                 1 0 | 1
//-                 1 1 | 0
//-
module \$_ANDNOT_ (A, B, Y);
input A, B;
output Y;
assign Y = A & (~B);
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ORNOT_ (A, B, Y)
//* group comb_combined
//-
//- A 2-input OR-NOT gate.
//-
//- Truth table:    A B | Y
//-                -----+---
//-                 0 0 | 1
//-                 0 1 | 0
//-                 1 0 | 1
//-                 1 1 | 1
//-
module \$_ORNOT_ (A, B, Y);
input A, B;
output Y;
assign Y = A | (~B);
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_MUX_ (A, B, S, Y)
//* group comb_simple
//-
//- A 2-input MUX gate.
//-
//- Truth table:    A B S | Y
//-                -------+---
//-                 a - 0 | a
//-                 - b 1 | b
//-
module \$_MUX_ (A, B, S, Y);
input A, B, S;
output Y;
assign Y = S ? B : A;
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_NMUX_ (A, B, S, Y)
//-
//- A 2-input inverting MUX gate.
//* group comb_combined
//-
//- Truth table:    A B S | Y
//-                -------+---
//-                 0 - 0 | 1
//-                 1 - 0 | 0
//-                 - 0 1 | 1
//-                 - 1 1 | 0
//-
module \$_NMUX_ (A, B, S, Y);
input A, B, S;
output Y;
assign Y = S ? !B : !A;
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_MUX4_ (A, B, C, D, S, T, Y)
//* group comb_combined
//-
//- A 4-input MUX gate.
//-
//- Truth table:    A B C D S T | Y
//-                -------------+---
//-                 a - - - 0 0 | a
//-                 - b - - 1 0 | b
//-                 - - c - 0 1 | c
//-                 - - - d 1 1 | d
//-
module \$_MUX4_ (A, B, C, D, S, T, Y);
input A, B, C, D, S, T;
output Y;
assign Y = T ? (S ? D : C) :
               (S ? B : A);
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_MUX8_ (A, B, C, D, E, F, G, H, S, T, U, Y)
//* group comb_combined
//-
//- An 8-input MUX gate.
//-
//- Truth table:    A B C D E F G H S T U | Y
//-                -----------------------+---
//-                 a - - - - - - - 0 0 0 | a
//-                 - b - - - - - - 1 0 0 | b
//-                 - - c - - - - - 0 1 0 | c
//-                 - - - d - - - - 1 1 0 | d
//-                 - - - - e - - - 0 0 1 | e
//-                 - - - - - f - - 1 0 1 | f
//-                 - - - - - - g - 0 1 1 | g
//-                 - - - - - - - h 1 1 1 | h
//-
module \$_MUX8_ (A, B, C, D, E, F, G, H, S, T, U, Y);
input A, B, C, D, E, F, G, H, S, T, U;
output Y;
assign Y = U ? T ? (S ? H : G) :
                   (S ? F : E) :
               T ? (S ? D : C) :
                   (S ? B : A);
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_MUX16_ (A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, S, T, U, V, Y)
//* group comb_combined
//-
//- A 16-input MUX gate.
//-
//- Truth table:    A B C D E F G H I J K L M N O P S T U V | Y
//-                -----------------------------------------+---
//-                 a - - - - - - - - - - - - - - - 0 0 0 0 | a
//-                 - b - - - - - - - - - - - - - - 1 0 0 0 | b
//-                 - - c - - - - - - - - - - - - - 0 1 0 0 | c
//-                 - - - d - - - - - - - - - - - - 1 1 0 0 | d
//-                 - - - - e - - - - - - - - - - - 0 0 1 0 | e
//-                 - - - - - f - - - - - - - - - - 1 0 1 0 | f
//-                 - - - - - - g - - - - - - - - - 0 1 1 0 | g
//-                 - - - - - - - h - - - - - - - - 1 1 1 0 | h
//-                 - - - - - - - - i - - - - - - - 0 0 0 1 | i
//-                 - - - - - - - - - j - - - - - - 1 0 0 1 | j
//-                 - - - - - - - - - - k - - - - - 0 1 0 1 | k
//-                 - - - - - - - - - - - l - - - - 1 1 0 1 | l
//-                 - - - - - - - - - - - - m - - - 0 0 1 1 | m
//-                 - - - - - - - - - - - - - n - - 1 0 1 1 | n
//-                 - - - - - - - - - - - - - - o - 0 1 1 1 | o
//-                 - - - - - - - - - - - - - - - p 1 1 1 1 | p
//-
module \$_MUX16_ (A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, S, T, U, V, Y);
input A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, S, T, U, V;
output Y;
assign Y = V ? U ? T ? (S ? P : O) :
                       (S ? N : M) :
                   T ? (S ? L : K) :
                       (S ? J : I) :
               U ? T ? (S ? H : G) :
                       (S ? F : E) :
                   T ? (S ? D : C) :
                       (S ? B : A);
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_AOI3_ (A, B, C, Y)
//* group comb_combined
//-
//- A 3-input And-Or-Invert gate.
//-
//- Truth table:    A B C | Y
//-                -------+---
//-                 0 0 0 | 1
//-                 0 0 1 | 0
//-                 0 1 0 | 1
//-                 0 1 1 | 0
//-                 1 0 0 | 1
//-                 1 0 1 | 0
//-                 1 1 0 | 0
//-                 1 1 1 | 0
//-
module \$_AOI3_ (A, B, C, Y);
input A, B, C;
output Y;
assign Y = ~((A & B) | C);
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_OAI3_ (A, B, C, Y)
//* group comb_combined
//-
//- A 3-input Or-And-Invert gate.
//-
//- Truth table:    A B C | Y
//-                -------+---
//-                 0 0 0 | 1
//-                 0 0 1 | 1
//-                 0 1 0 | 1
//-                 0 1 1 | 0
//-                 1 0 0 | 1
//-                 1 0 1 | 0
//-                 1 1 0 | 1
//-                 1 1 1 | 0
//-
module \$_OAI3_ (A, B, C, Y);
input A, B, C;
output Y;
assign Y = ~((A | B) & C);
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_AOI4_ (A, B, C, Y)
//* group comb_combined
//-
//- A 4-input And-Or-Invert gate.
//-
//- Truth table:    A B C D | Y
//-                ---------+---
//-                 0 0 0 0 | 1
//-                 0 0 0 1 | 1
//-                 0 0 1 0 | 1
//-                 0 0 1 1 | 0
//-                 0 1 0 0 | 1
//-                 0 1 0 1 | 1
//-                 0 1 1 0 | 1
//-                 0 1 1 1 | 0
//-                 1 0 0 0 | 1
//-                 1 0 0 1 | 1
//-                 1 0 1 0 | 1
//-                 1 0 1 1 | 0
//-                 1 1 0 0 | 0
//-                 1 1 0 1 | 0
//-                 1 1 1 0 | 0
//-                 1 1 1 1 | 0
//-
module \$_AOI4_ (A, B, C, D, Y);
input A, B, C, D;
output Y;
assign Y = ~((A & B) | (C & D));
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_OAI4_ (A, B, C, Y)
//* group comb_combined
//-
//- A 4-input Or-And-Invert gate.
//-
//- Truth table:    A B C D | Y
//-                ---------+---
//-                 0 0 0 0 | 1
//-                 0 0 0 1 | 1
//-                 0 0 1 0 | 1
//-                 0 0 1 1 | 1
//-                 0 1 0 0 | 1
//-                 0 1 0 1 | 0
//-                 0 1 1 0 | 0
//-                 0 1 1 1 | 0
//-                 1 0 0 0 | 1
//-                 1 0 0 1 | 0
//-                 1 0 1 0 | 0
//-                 1 0 1 1 | 0
//-                 1 1 0 0 | 1
//-                 1 1 0 1 | 0
//-                 1 1 1 0 | 0
//-                 1 1 1 1 | 0
//-
module \$_OAI4_ (A, B, C, D, Y);
input A, B, C, D;
output Y;
assign Y = ~((A | B) & (C | D));
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_TBUF_ (A, E, Y)
//* group gate_other
//-
//- A tri-state buffer.
//-
//- Truth table:    A E | Y
//-                -----+---
//-                 a 1 | a
//-                 - 0 | z
//-
module \$_TBUF_ (A, E, Y);
input A, E;
output Y;
assign Y = E ? A : 1'bz;
endmodule

// NOTE: the following cell types are autogenerated.  DO NOT EDIT them manually,
// instead edit the templates in gen_ff_types.py and rerun it.

// START AUTOGENERATED CELL TYPES

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SR_NN_ (S, R, Q)
//* group reg_latch
//-
//- A set-reset latch with negative polarity SET and negative polarity RESET.
//-
//- Truth table:    S R | Q
//-                -----+---
//-                 - 0 | 0
//-                 0 - | 1
//-                 - - | q
//-
module \$_SR_NN_ (S, R, Q);
input S, R;
output reg Q;
always @* begin
	if (R == 0)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SR_NP_ (S, R, Q)
//* group reg_latch
//-
//- A set-reset latch with negative polarity SET and positive polarity RESET.
//-
//- Truth table:    S R | Q
//-                -----+---
//-                 - 1 | 0
//-                 0 - | 1
//-                 - - | q
//-
module \$_SR_NP_ (S, R, Q);
input S, R;
output reg Q;
always @* begin
	if (R == 1)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SR_PN_ (S, R, Q)
//* group reg_latch
//-
//- A set-reset latch with positive polarity SET and negative polarity RESET.
//-
//- Truth table:    S R | Q
//-                -----+---
//-                 - 0 | 0
//-                 1 - | 1
//-                 - - | q
//-
module \$_SR_PN_ (S, R, Q);
input S, R;
output reg Q;
always @* begin
	if (R == 0)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SR_PP_ (S, R, Q)
//* group reg_latch
//-
//- A set-reset latch with positive polarity SET and positive polarity RESET.
//-
//- Truth table:    S R | Q
//-                -----+---
//-                 - 1 | 0
//-                 1 - | 1
//-                 - - | q
//-
module \$_SR_PP_ (S, R, Q);
input S, R;
output reg Q;
always @* begin
	if (R == 1)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
end
endmodule

`ifdef SIMCELLS_FF
//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_FF_ (D, Q)
//* group reg_ff
//-
//- A D-type flip-flop that is clocked from the implicit global clock. (This cell
//- type is usually only used in netlists for formal verification.)
//-
module \$_FF_ (D, Q);
input D;
output reg Q;
always @($global_clock) begin
	Q <= D;
end
endmodule
`endif

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFF_N_ (D, C, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop.
//-
//- Truth table:    D C | Q
//-                -----+---
//-                 d \ | d
//-                 - - | q
//-
module \$_DFF_N_ (D, C, Q);
input D, C;
output reg Q;
always @(negedge C) begin
	Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFF_P_ (D, C, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop.
//-
//- Truth table:    D C | Q
//-                -----+---
//-                 d / | d
//-                 - - | q
//-
module \$_DFF_P_ (D, C, Q);
input D, C;
output reg Q;
always @(posedge C) begin
	Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_NN_ (D, C, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity enable.
//-
//- Truth table:    D C E | Q
//-                -------+---
//-                 d \ 0 | d
//-                 - - - | q
//-
module \$_DFFE_NN_ (D, C, E, Q);
input D, C, E;
output reg Q;
always @(negedge C) begin
	if (!E) Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_NP_ (D, C, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity enable.
//-
//- Truth table:    D C E | Q
//-                -------+---
//-                 d \ 1 | d
//-                 - - - | q
//-
module \$_DFFE_NP_ (D, C, E, Q);
input D, C, E;
output reg Q;
always @(negedge C) begin
	if (E) Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_PN_ (D, C, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity enable.
//-
//- Truth table:    D C E | Q
//-                -------+---
//-                 d / 0 | d
//-                 - - - | q
//-
module \$_DFFE_PN_ (D, C, E, Q);
input D, C, E;
output reg Q;
always @(posedge C) begin
	if (!E) Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_PP_ (D, C, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity enable.
//-
//- Truth table:    D C E | Q
//-                -------+---
//-                 d / 1 | d
//-                 - - - | q
//-
module \$_DFFE_PP_ (D, C, E, Q);
input D, C, E;
output reg Q;
always @(posedge C) begin
	if (E) Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFF_NN0_ (D, C, R, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity reset.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - - 0 | 0
//-                 d \ - | d
//-                 - - - | q
//-
module \$_DFF_NN0_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(negedge C or negedge R) begin
	if (R == 0)
		Q <= 0;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFF_NN1_ (D, C, R, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity set.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - - 0 | 1
//-                 d \ - | d
//-                 - - - | q
//-
module \$_DFF_NN1_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(negedge C or negedge R) begin
	if (R == 0)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFF_NP0_ (D, C, R, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity reset.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - - 1 | 0
//-                 d \ - | d
//-                 - - - | q
//-
module \$_DFF_NP0_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(negedge C or posedge R) begin
	if (R == 1)
		Q <= 0;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFF_NP1_ (D, C, R, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity set.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - - 1 | 1
//-                 d \ - | d
//-                 - - - | q
//-
module \$_DFF_NP1_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(negedge C or posedge R) begin
	if (R == 1)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFF_PN0_ (D, C, R, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity reset.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - - 0 | 0
//-                 d / - | d
//-                 - - - | q
//-
module \$_DFF_PN0_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(posedge C or negedge R) begin
	if (R == 0)
		Q <= 0;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFF_PN1_ (D, C, R, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity set.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - - 0 | 1
//-                 d / - | d
//-                 - - - | q
//-
module \$_DFF_PN1_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(posedge C or negedge R) begin
	if (R == 0)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFF_PP0_ (D, C, R, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity reset.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - - 1 | 0
//-                 d / - | d
//-                 - - - | q
//-
module \$_DFF_PP0_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(posedge C or posedge R) begin
	if (R == 1)
		Q <= 0;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFF_PP1_ (D, C, R, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity set.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - - 1 | 1
//-                 d / - | d
//-                 - - - | q
//-
module \$_DFF_PP1_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(posedge C or posedge R) begin
	if (R == 1)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_NN0N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity reset and negative
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 0 - | 0
//-                 d \ - 0 | d
//-                 - - - - | q
//-
module \$_DFFE_NN0N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C or negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_NN0P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity reset and positive
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 0 - | 0
//-                 d \ - 1 | d
//-                 - - - - | q
//-
module \$_DFFE_NN0P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C or negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_NN1N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity set and negative
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 0 - | 1
//-                 d \ - 0 | d
//-                 - - - - | q
//-
module \$_DFFE_NN1N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C or negedge R) begin
	if (R == 0)
		Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_NN1P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity set and positive
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 0 - | 1
//-                 d \ - 1 | d
//-                 - - - - | q
//-
module \$_DFFE_NN1P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C or negedge R) begin
	if (R == 0)
		Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_NP0N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity reset and negative
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 1 - | 0
//-                 d \ - 0 | d
//-                 - - - - | q
//-
module \$_DFFE_NP0N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C or posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_NP0P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity reset and positive
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 1 - | 0
//-                 d \ - 1 | d
//-                 - - - - | q
//-
module \$_DFFE_NP0P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C or posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_NP1N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity set and negative
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 1 - | 1
//-                 d \ - 0 | d
//-                 - - - - | q
//-
module \$_DFFE_NP1N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C or posedge R) begin
	if (R == 1)
		Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_NP1P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity set and positive
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 1 - | 1
//-                 d \ - 1 | d
//-                 - - - - | q
//-
module \$_DFFE_NP1P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C or posedge R) begin
	if (R == 1)
		Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_PN0N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity reset and negative
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 0 - | 0
//-                 d / - 0 | d
//-                 - - - - | q
//-
module \$_DFFE_PN0N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C or negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_PN0P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity reset and positive
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 0 - | 0
//-                 d / - 1 | d
//-                 - - - - | q
//-
module \$_DFFE_PN0P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C or negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_PN1N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity set and negative
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 0 - | 1
//-                 d / - 0 | d
//-                 - - - - | q
//-
module \$_DFFE_PN1N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C or negedge R) begin
	if (R == 0)
		Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_PN1P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity set and positive
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 0 - | 1
//-                 d / - 1 | d
//-                 - - - - | q
//-
module \$_DFFE_PN1P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C or negedge R) begin
	if (R == 0)
		Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_PP0N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity reset and negative
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 1 - | 0
//-                 d / - 0 | d
//-                 - - - - | q
//-
module \$_DFFE_PP0N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C or posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_PP0P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity reset and positive
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 1 - | 0
//-                 d / - 1 | d
//-                 - - - - | q
//-
module \$_DFFE_PP0P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C or posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_PP1N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity set and negative
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 1 - | 1
//-                 d / - 0 | d
//-                 - - - - | q
//-
module \$_DFFE_PP1N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C or posedge R) begin
	if (R == 1)
		Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFE_PP1P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity set and positive
//- polarity clock enable.
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - - 1 - | 1
//-                 d / - 1 | d
//-                 - - - - | q
//-
module \$_DFFE_PP1P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C or posedge R) begin
	if (R == 1)
		Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ALDFF_NN_ (D, C, L, AD, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity async load.
//-
//- Truth table:    D C L AD | Q
//-                ----------+---
//-                 - - 0 a  | a
//-                 d \ - -  | d
//-                 - - - -  | q
//-
module \$_ALDFF_NN_ (D, C, L, AD, Q);
input D, C, L, AD;
output reg Q;
always @(negedge C or negedge L) begin
	if (L == 0)
		Q <= AD;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ALDFF_NP_ (D, C, L, AD, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity async load.
//-
//- Truth table:    D C L AD | Q
//-                ----------+---
//-                 - - 1 a  | a
//-                 d \ - -  | d
//-                 - - - -  | q
//-
module \$_ALDFF_NP_ (D, C, L, AD, Q);
input D, C, L, AD;
output reg Q;
always @(negedge C or posedge L) begin
	if (L == 1)
		Q <= AD;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ALDFF_PN_ (D, C, L, AD, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity async load.
//-
//- Truth table:    D C L AD | Q
//-                ----------+---
//-                 - - 0 a  | a
//-                 d / - -  | d
//-                 - - - -  | q
//-
module \$_ALDFF_PN_ (D, C, L, AD, Q);
input D, C, L, AD;
output reg Q;
always @(posedge C or negedge L) begin
	if (L == 0)
		Q <= AD;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ALDFF_PP_ (D, C, L, AD, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity async load.
//-
//- Truth table:    D C L AD | Q
//-                ----------+---
//-                 - - 1 a  | a
//-                 d / - -  | d
//-                 - - - -  | q
//-
module \$_ALDFF_PP_ (D, C, L, AD, Q);
input D, C, L, AD;
output reg Q;
always @(posedge C or posedge L) begin
	if (L == 1)
		Q <= AD;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ALDFFE_NNN_ (D, C, L, AD, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity async load and negative
//- polarity clock enable.
//-
//- Truth table:    D C L AD E | Q
//-                ------------+---
//-                 - - 0 a  - | a
//-                 d \ - -  0 | d
//-                 - - - -  - | q
//-
module \$_ALDFFE_NNN_ (D, C, L, AD, E, Q);
input D, C, L, AD, E;
output reg Q;
always @(negedge C or negedge L) begin
	if (L == 0)
		Q <= AD;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ALDFFE_NNP_ (D, C, L, AD, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity async load and positive
//- polarity clock enable.
//-
//- Truth table:    D C L AD E | Q
//-                ------------+---
//-                 - - 0 a  - | a
//-                 d \ - -  1 | d
//-                 - - - -  - | q
//-
module \$_ALDFFE_NNP_ (D, C, L, AD, E, Q);
input D, C, L, AD, E;
output reg Q;
always @(negedge C or negedge L) begin
	if (L == 0)
		Q <= AD;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ALDFFE_NPN_ (D, C, L, AD, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity async load and negative
//- polarity clock enable.
//-
//- Truth table:    D C L AD E | Q
//-                ------------+---
//-                 - - 1 a  - | a
//-                 d \ - -  0 | d
//-                 - - - -  - | q
//-
module \$_ALDFFE_NPN_ (D, C, L, AD, E, Q);
input D, C, L, AD, E;
output reg Q;
always @(negedge C or posedge L) begin
	if (L == 1)
		Q <= AD;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ALDFFE_NPP_ (D, C, L, AD, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity async load and positive
//- polarity clock enable.
//-
//- Truth table:    D C L AD E | Q
//-                ------------+---
//-                 - - 1 a  - | a
//-                 d \ - -  1 | d
//-                 - - - -  - | q
//-
module \$_ALDFFE_NPP_ (D, C, L, AD, E, Q);
input D, C, L, AD, E;
output reg Q;
always @(negedge C or posedge L) begin
	if (L == 1)
		Q <= AD;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ALDFFE_PNN_ (D, C, L, AD, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity async load and negative
//- polarity clock enable.
//-
//- Truth table:    D C L AD E | Q
//-                ------------+---
//-                 - - 0 a  - | a
//-                 d / - -  0 | d
//-                 - - - -  - | q
//-
module \$_ALDFFE_PNN_ (D, C, L, AD, E, Q);
input D, C, L, AD, E;
output reg Q;
always @(posedge C or negedge L) begin
	if (L == 0)
		Q <= AD;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ALDFFE_PNP_ (D, C, L, AD, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity async load and positive
//- polarity clock enable.
//-
//- Truth table:    D C L AD E | Q
//-                ------------+---
//-                 - - 0 a  - | a
//-                 d / - -  1 | d
//-                 - - - -  - | q
//-
module \$_ALDFFE_PNP_ (D, C, L, AD, E, Q);
input D, C, L, AD, E;
output reg Q;
always @(posedge C or negedge L) begin
	if (L == 0)
		Q <= AD;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ALDFFE_PPN_ (D, C, L, AD, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity async load and negative
//- polarity clock enable.
//-
//- Truth table:    D C L AD E | Q
//-                ------------+---
//-                 - - 1 a  - | a
//-                 d / - -  0 | d
//-                 - - - -  - | q
//-
module \$_ALDFFE_PPN_ (D, C, L, AD, E, Q);
input D, C, L, AD, E;
output reg Q;
always @(posedge C or posedge L) begin
	if (L == 1)
		Q <= AD;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_ALDFFE_PPP_ (D, C, L, AD, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity async load and positive
//- polarity clock enable.
//-
//- Truth table:    D C L AD E | Q
//-                ------------+---
//-                 - - 1 a  - | a
//-                 d / - -  1 | d
//-                 - - - -  - | q
//-
module \$_ALDFFE_PPP_ (D, C, L, AD, E, Q);
input D, C, L, AD, E;
output reg Q;
always @(posedge C or posedge L) begin
	if (L == 1)
		Q <= AD;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSR_NNN_ (C, S, R, D, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity set and negative
//- polarity reset.
//-
//- Truth table:    C S R D | Q
//-                ---------+---
//-                 - - 0 - | 0
//-                 - 0 - - | 1
//-                 \ - - d | d
//-                 - - - - | q
//-
module \$_DFFSR_NNN_ (C, S, R, D, Q);
input C, S, R, D;
output reg Q;
always @(negedge C, negedge S, negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSR_NNP_ (C, S, R, D, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity set and positive
//- polarity reset.
//-
//- Truth table:    C S R D | Q
//-                ---------+---
//-                 - - 1 - | 0
//-                 - 0 - - | 1
//-                 \ - - d | d
//-                 - - - - | q
//-
module \$_DFFSR_NNP_ (C, S, R, D, Q);
input C, S, R, D;
output reg Q;
always @(negedge C, negedge S, posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSR_NPN_ (C, S, R, D, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity set and negative
//- polarity reset.
//-
//- Truth table:    C S R D | Q
//-                ---------+---
//-                 - - 0 - | 0
//-                 - 1 - - | 1
//-                 \ - - d | d
//-                 - - - - | q
//-
module \$_DFFSR_NPN_ (C, S, R, D, Q);
input C, S, R, D;
output reg Q;
always @(negedge C, posedge S, negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSR_NPP_ (C, S, R, D, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity set and positive
//- polarity reset.
//-
//- Truth table:    C S R D | Q
//-                ---------+---
//-                 - - 1 - | 0
//-                 - 1 - - | 1
//-                 \ - - d | d
//-                 - - - - | q
//-
module \$_DFFSR_NPP_ (C, S, R, D, Q);
input C, S, R, D;
output reg Q;
always @(negedge C, posedge S, posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSR_PNN_ (C, S, R, D, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity set and negative
//- polarity reset.
//-
//- Truth table:    C S R D | Q
//-                ---------+---
//-                 - - 0 - | 0
//-                 - 0 - - | 1
//-                 / - - d | d
//-                 - - - - | q
//-
module \$_DFFSR_PNN_ (C, S, R, D, Q);
input C, S, R, D;
output reg Q;
always @(posedge C, negedge S, negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSR_PNP_ (C, S, R, D, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity set and positive
//- polarity reset.
//-
//- Truth table:    C S R D | Q
//-                ---------+---
//-                 - - 1 - | 0
//-                 - 0 - - | 1
//-                 / - - d | d
//-                 - - - - | q
//-
module \$_DFFSR_PNP_ (C, S, R, D, Q);
input C, S, R, D;
output reg Q;
always @(posedge C, negedge S, posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSR_PPN_ (C, S, R, D, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity set and negative
//- polarity reset.
//-
//- Truth table:    C S R D | Q
//-                ---------+---
//-                 - - 0 - | 0
//-                 - 1 - - | 1
//-                 / - - d | d
//-                 - - - - | q
//-
module \$_DFFSR_PPN_ (C, S, R, D, Q);
input C, S, R, D;
output reg Q;
always @(posedge C, posedge S, negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSR_PPP_ (C, S, R, D, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity set and positive
//- polarity reset.
//-
//- Truth table:    C S R D | Q
//-                ---------+---
//-                 - - 1 - | 0
//-                 - 1 - - | 1
//-                 / - - d | d
//-                 - - - - | q
//-
module \$_DFFSR_PPP_ (C, S, R, D, Q);
input C, S, R, D;
output reg Q;
always @(posedge C, posedge S, posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_NNNN_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity set, negative
//- polarity reset and negative polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 0 - - | 0
//-                 - 0 - - - | 1
//-                 \ - - 0 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_NNNN_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(negedge C, negedge S, negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
        else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_NNNP_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity set, negative
//- polarity reset and positive polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 0 - - | 0
//-                 - 0 - - - | 1
//-                 \ - - 1 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_NNNP_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(negedge C, negedge S, negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
        else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_NNPN_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity set, positive
//- polarity reset and negative polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 1 - - | 0
//-                 - 0 - - - | 1
//-                 \ - - 0 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_NNPN_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(negedge C, negedge S, posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
        else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_NNPP_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity set, positive
//- polarity reset and positive polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 1 - - | 0
//-                 - 0 - - - | 1
//-                 \ - - 1 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_NNPP_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(negedge C, negedge S, posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
        else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_NPNN_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity set, negative
//- polarity reset and negative polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 0 - - | 0
//-                 - 1 - - - | 1
//-                 \ - - 0 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_NPNN_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(negedge C, posedge S, negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
        else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_NPNP_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity set, negative
//- polarity reset and positive polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 0 - - | 0
//-                 - 1 - - - | 1
//-                 \ - - 1 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_NPNP_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(negedge C, posedge S, negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
        else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_NPPN_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity set, positive
//- polarity reset and negative polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 1 - - | 0
//-                 - 1 - - - | 1
//-                 \ - - 0 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_NPPN_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(negedge C, posedge S, posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
        else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_NPPP_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity set, positive
//- polarity reset and positive polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 1 - - | 0
//-                 - 1 - - - | 1
//-                 \ - - 1 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_NPPP_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(negedge C, posedge S, posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
        else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_PNNN_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity set, negative
//- polarity reset and negative polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 0 - - | 0
//-                 - 0 - - - | 1
//-                 / - - 0 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_PNNN_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(posedge C, negedge S, negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
        else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_PNNP_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity set, negative
//- polarity reset and positive polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 0 - - | 0
//-                 - 0 - - - | 1
//-                 / - - 1 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_PNNP_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(posedge C, negedge S, negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
        else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_PNPN_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity set, positive
//- polarity reset and negative polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 1 - - | 0
//-                 - 0 - - - | 1
//-                 / - - 0 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_PNPN_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(posedge C, negedge S, posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
        else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_PNPP_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity set, positive
//- polarity reset and positive polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 1 - - | 0
//-                 - 0 - - - | 1
//-                 / - - 1 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_PNPP_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(posedge C, negedge S, posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
        else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_PPNN_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity set, negative
//- polarity reset and negative polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 0 - - | 0
//-                 - 1 - - - | 1
//-                 / - - 0 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_PPNN_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(posedge C, posedge S, negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
        else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_PPNP_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity set, negative
//- polarity reset and positive polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 0 - - | 0
//-                 - 1 - - - | 1
//-                 / - - 1 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_PPNP_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(posedge C, posedge S, negedge R) begin
	if (R == 0)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
        else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_PPPN_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity set, positive
//- polarity reset and negative polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 1 - - | 0
//-                 - 1 - - - | 1
//-                 / - - 0 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_PPPN_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(posedge C, posedge S, posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
        else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DFFSRE_PPPP_ (C, S, R, E, D, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity set, positive
//- polarity reset and positive polarity clock enable.
//-
//- Truth table:    C S R E D | Q
//-                -----------+---
//-                 - - 1 - - | 0
//-                 - 1 - - - | 1
//-                 / - - 1 d | d
//-                 - - - - - | q
//-
module \$_DFFSRE_PPPP_ (C, S, R, E, D, Q);
input C, S, R, E, D;
output reg Q;
always @(posedge C, posedge S, posedge R) begin
	if (R == 1)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
        else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFF_NN0_ (D, C, R, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity synchronous reset.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - \ 0 | 0
//-                 d \ - | d
//-                 - - - | q
//-
module \$_SDFF_NN0_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(negedge C) begin
	if (R == 0)
		Q <= 0;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFF_NN1_ (D, C, R, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity synchronous set.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - \ 0 | 1
//-                 d \ - | d
//-                 - - - | q
//-
module \$_SDFF_NN1_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(negedge C) begin
	if (R == 0)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFF_NP0_ (D, C, R, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity synchronous reset.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - \ 1 | 0
//-                 d \ - | d
//-                 - - - | q
//-
module \$_SDFF_NP0_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(negedge C) begin
	if (R == 1)
		Q <= 0;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFF_NP1_ (D, C, R, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity synchronous set.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - \ 1 | 1
//-                 d \ - | d
//-                 - - - | q
//-
module \$_SDFF_NP1_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(negedge C) begin
	if (R == 1)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFF_PN0_ (D, C, R, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity synchronous reset.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - / 0 | 0
//-                 d / - | d
//-                 - - - | q
//-
module \$_SDFF_PN0_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(posedge C) begin
	if (R == 0)
		Q <= 0;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFF_PN1_ (D, C, R, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity synchronous set.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - / 0 | 1
//-                 d / - | d
//-                 - - - | q
//-
module \$_SDFF_PN1_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(posedge C) begin
	if (R == 0)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFF_PP0_ (D, C, R, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity synchronous reset.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - / 1 | 0
//-                 d / - | d
//-                 - - - | q
//-
module \$_SDFF_PP0_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(posedge C) begin
	if (R == 1)
		Q <= 0;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFF_PP1_ (D, C, R, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity synchronous set.
//-
//- Truth table:    D C R | Q
//-                -------+---
//-                 - / 1 | 1
//-                 d / - | d
//-                 - - - | q
//-
module \$_SDFF_PP1_ (D, C, R, Q);
input D, C, R;
output reg Q;
always @(posedge C) begin
	if (R == 1)
		Q <= 1;
	else
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_NN0N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity synchronous reset and negative
//- polarity clock enable (with reset having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 0 - | 0
//-                 d \ - 0 | d
//-                 - - - - | q
//-
module \$_SDFFE_NN0N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (R == 0)
		Q <= 0;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_NN0P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity synchronous reset and positive
//- polarity clock enable (with reset having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 0 - | 0
//-                 d \ - 1 | d
//-                 - - - - | q
//-
module \$_SDFFE_NN0P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (R == 0)
		Q <= 0;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_NN1N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity synchronous set and negative
//- polarity clock enable (with set having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 0 - | 1
//-                 d \ - 0 | d
//-                 - - - - | q
//-
module \$_SDFFE_NN1N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (R == 0)
		Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_NN1P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity synchronous set and positive
//- polarity clock enable (with set having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 0 - | 1
//-                 d \ - 1 | d
//-                 - - - - | q
//-
module \$_SDFFE_NN1P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (R == 0)
		Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_NP0N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity synchronous reset and negative
//- polarity clock enable (with reset having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 1 - | 0
//-                 d \ - 0 | d
//-                 - - - - | q
//-
module \$_SDFFE_NP0N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (R == 1)
		Q <= 0;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_NP0P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity synchronous reset and positive
//- polarity clock enable (with reset having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 1 - | 0
//-                 d \ - 1 | d
//-                 - - - - | q
//-
module \$_SDFFE_NP0P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (R == 1)
		Q <= 0;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_NP1N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity synchronous set and negative
//- polarity clock enable (with set having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 1 - | 1
//-                 d \ - 0 | d
//-                 - - - - | q
//-
module \$_SDFFE_NP1N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (R == 1)
		Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_NP1P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity synchronous set and positive
//- polarity clock enable (with set having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 1 - | 1
//-                 d \ - 1 | d
//-                 - - - - | q
//-
module \$_SDFFE_NP1P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (R == 1)
		Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_PN0N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity synchronous reset and negative
//- polarity clock enable (with reset having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 0 - | 0
//-                 d / - 0 | d
//-                 - - - - | q
//-
module \$_SDFFE_PN0N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (R == 0)
		Q <= 0;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_PN0P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity synchronous reset and positive
//- polarity clock enable (with reset having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 0 - | 0
//-                 d / - 1 | d
//-                 - - - - | q
//-
module \$_SDFFE_PN0P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (R == 0)
		Q <= 0;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_PN1N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity synchronous set and negative
//- polarity clock enable (with set having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 0 - | 1
//-                 d / - 0 | d
//-                 - - - - | q
//-
module \$_SDFFE_PN1N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (R == 0)
		Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_PN1P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity synchronous set and positive
//- polarity clock enable (with set having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 0 - | 1
//-                 d / - 1 | d
//-                 - - - - | q
//-
module \$_SDFFE_PN1P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (R == 0)
		Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_PP0N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity synchronous reset and negative
//- polarity clock enable (with reset having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 1 - | 0
//-                 d / - 0 | d
//-                 - - - - | q
//-
module \$_SDFFE_PP0N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (R == 1)
		Q <= 0;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_PP0P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity synchronous reset and positive
//- polarity clock enable (with reset having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 1 - | 0
//-                 d / - 1 | d
//-                 - - - - | q
//-
module \$_SDFFE_PP0P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (R == 1)
		Q <= 0;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_PP1N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity synchronous set and negative
//- polarity clock enable (with set having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 1 - | 1
//-                 d / - 0 | d
//-                 - - - - | q
//-
module \$_SDFFE_PP1N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (R == 1)
		Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFE_PP1P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity synchronous set and positive
//- polarity clock enable (with set having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 1 - | 1
//-                 d / - 1 | d
//-                 - - - - | q
//-
module \$_SDFFE_PP1P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (R == 1)
		Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_NN0N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity synchronous reset and negative
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 0 0 | 0
//-                 d \ - 0 | d
//-                 - - - - | q
//-
module \$_SDFFCE_NN0N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (E == 0) begin
		if (R == 0)
			Q <= 0;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_NN0P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity synchronous reset and positive
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 0 1 | 0
//-                 d \ - 1 | d
//-                 - - - - | q
//-
module \$_SDFFCE_NN0P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (E == 1) begin
		if (R == 0)
			Q <= 0;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_NN1N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity synchronous set and negative
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 0 0 | 1
//-                 d \ - 0 | d
//-                 - - - - | q
//-
module \$_SDFFCE_NN1N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (E == 0) begin
		if (R == 0)
			Q <= 1;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_NN1P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with negative polarity synchronous set and positive
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 0 1 | 1
//-                 d \ - 1 | d
//-                 - - - - | q
//-
module \$_SDFFCE_NN1P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (E == 1) begin
		if (R == 0)
			Q <= 1;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_NP0N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity synchronous reset and negative
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 1 0 | 0
//-                 d \ - 0 | d
//-                 - - - - | q
//-
module \$_SDFFCE_NP0N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (E == 0) begin
		if (R == 1)
			Q <= 0;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_NP0P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity synchronous reset and positive
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 1 1 | 0
//-                 d \ - 1 | d
//-                 - - - - | q
//-
module \$_SDFFCE_NP0P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (E == 1) begin
		if (R == 1)
			Q <= 0;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_NP1N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity synchronous set and negative
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 1 0 | 1
//-                 d \ - 0 | d
//-                 - - - - | q
//-
module \$_SDFFCE_NP1N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (E == 0) begin
		if (R == 1)
			Q <= 1;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_NP1P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A negative edge D-type flip-flop with positive polarity synchronous set and positive
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - \ 1 1 | 1
//-                 d \ - 1 | d
//-                 - - - - | q
//-
module \$_SDFFCE_NP1P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(negedge C) begin
	if (E == 1) begin
		if (R == 1)
			Q <= 1;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_PN0N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity synchronous reset and negative
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 0 0 | 0
//-                 d / - 0 | d
//-                 - - - - | q
//-
module \$_SDFFCE_PN0N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (E == 0) begin
		if (R == 0)
			Q <= 0;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_PN0P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity synchronous reset and positive
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 0 1 | 0
//-                 d / - 1 | d
//-                 - - - - | q
//-
module \$_SDFFCE_PN0P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (E == 1) begin
		if (R == 0)
			Q <= 0;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_PN1N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity synchronous set and negative
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 0 0 | 1
//-                 d / - 0 | d
//-                 - - - - | q
//-
module \$_SDFFCE_PN1N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (E == 0) begin
		if (R == 0)
			Q <= 1;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_PN1P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with negative polarity synchronous set and positive
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 0 1 | 1
//-                 d / - 1 | d
//-                 - - - - | q
//-
module \$_SDFFCE_PN1P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (E == 1) begin
		if (R == 0)
			Q <= 1;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_PP0N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity synchronous reset and negative
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 1 0 | 0
//-                 d / - 0 | d
//-                 - - - - | q
//-
module \$_SDFFCE_PP0N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (E == 0) begin
		if (R == 1)
			Q <= 0;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_PP0P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity synchronous reset and positive
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 1 1 | 0
//-                 d / - 1 | d
//-                 - - - - | q
//-
module \$_SDFFCE_PP0P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (E == 1) begin
		if (R == 1)
			Q <= 0;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_PP1N_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity synchronous set and negative
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 1 0 | 1
//-                 d / - 0 | d
//-                 - - - - | q
//-
module \$_SDFFCE_PP1N_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (E == 0) begin
		if (R == 1)
			Q <= 1;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_SDFFCE_PP1P_ (D, C, R, E, Q)
//* group reg_ff
//-
//- A positive edge D-type flip-flop with positive polarity synchronous set and positive
//- polarity clock enable (with clock enable having priority).
//-
//- Truth table:    D C R E | Q
//-                ---------+---
//-                 - / 1 1 | 1
//-                 d / - 1 | d
//-                 - - - - | q
//-
module \$_SDFFCE_PP1P_ (D, C, R, E, Q);
input D, C, R, E;
output reg Q;
always @(posedge C) begin
	if (E == 1) begin
		if (R == 1)
			Q <= 1;
		else
			Q <= D;
	end
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCH_N_ (E, D, Q)
//* group reg_latch
//-
//- A negative enable D-type latch.
//-
//- Truth table:    E D | Q
//-                -----+---
//-                 0 d | d
//-                 - - | q
//-
module \$_DLATCH_N_ (E, D, Q);
input E, D;
output reg Q;
always @* begin
	if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCH_P_ (E, D, Q)
//* group reg_latch
//-
//- A positive enable D-type latch.
//-
//- Truth table:    E D | Q
//-                -----+---
//-                 1 d | d
//-                 - - | q
//-
module \$_DLATCH_P_ (E, D, Q);
input E, D;
output reg Q;
always @* begin
	if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCH_NN0_ (E, R, D, Q)
//* group reg_latch
//-
//- A negative enable D-type latch with negative polarity reset.
//-
//- Truth table:    E R D | Q
//-                -------+---
//-                 - 0 - | 0
//-                 0 - d | d
//-                 - - - | q
//-
module \$_DLATCH_NN0_ (E, R, D, Q);
input E, R, D;
output reg Q;
always @* begin
	if (R == 0)
                Q <= 0;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCH_NN1_ (E, R, D, Q)
//* group reg_latch
//-
//- A negative enable D-type latch with negative polarity set.
//-
//- Truth table:    E R D | Q
//-                -------+---
//-                 - 0 - | 1
//-                 0 - d | d
//-                 - - - | q
//-
module \$_DLATCH_NN1_ (E, R, D, Q);
input E, R, D;
output reg Q;
always @* begin
	if (R == 0)
                Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCH_NP0_ (E, R, D, Q)
//* group reg_latch
//-
//- A negative enable D-type latch with positive polarity reset.
//-
//- Truth table:    E R D | Q
//-                -------+---
//-                 - 1 - | 0
//-                 0 - d | d
//-                 - - - | q
//-
module \$_DLATCH_NP0_ (E, R, D, Q);
input E, R, D;
output reg Q;
always @* begin
	if (R == 1)
                Q <= 0;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCH_NP1_ (E, R, D, Q)
//* group reg_latch
//-
//- A negative enable D-type latch with positive polarity set.
//-
//- Truth table:    E R D | Q
//-                -------+---
//-                 - 1 - | 1
//-                 0 - d | d
//-                 - - - | q
//-
module \$_DLATCH_NP1_ (E, R, D, Q);
input E, R, D;
output reg Q;
always @* begin
	if (R == 1)
                Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCH_PN0_ (E, R, D, Q)
//* group reg_latch
//-
//- A positive enable D-type latch with negative polarity reset.
//-
//- Truth table:    E R D | Q
//-                -------+---
//-                 - 0 - | 0
//-                 1 - d | d
//-                 - - - | q
//-
module \$_DLATCH_PN0_ (E, R, D, Q);
input E, R, D;
output reg Q;
always @* begin
	if (R == 0)
                Q <= 0;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCH_PN1_ (E, R, D, Q)
//* group reg_latch
//-
//- A positive enable D-type latch with negative polarity set.
//-
//- Truth table:    E R D | Q
//-                -------+---
//-                 - 0 - | 1
//-                 1 - d | d
//-                 - - - | q
//-
module \$_DLATCH_PN1_ (E, R, D, Q);
input E, R, D;
output reg Q;
always @* begin
	if (R == 0)
                Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCH_PP0_ (E, R, D, Q)
//* group reg_latch
//-
//- A positive enable D-type latch with positive polarity reset.
//-
//- Truth table:    E R D | Q
//-                -------+---
//-                 - 1 - | 0
//-                 1 - d | d
//-                 - - - | q
//-
module \$_DLATCH_PP0_ (E, R, D, Q);
input E, R, D;
output reg Q;
always @* begin
	if (R == 1)
                Q <= 0;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCH_PP1_ (E, R, D, Q)
//* group reg_latch
//-
//- A positive enable D-type latch with positive polarity set.
//-
//- Truth table:    E R D | Q
//-                -------+---
//-                 - 1 - | 1
//-                 1 - d | d
//-                 - - - | q
//-
module \$_DLATCH_PP1_ (E, R, D, Q);
input E, R, D;
output reg Q;
always @* begin
	if (R == 1)
                Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCHSR_NNN_ (E, S, R, D, Q)
//* group reg_latch
//-
//- A negative enable D-type latch with negative polarity set and negative
//- polarity reset.
//-
//- Truth table:    E S R D | Q
//-                ---------+---
//-                 - - 0 - | 0
//-                 - 0 - - | 1
//-                 0 - - d | d
//-                 - - - - | q
//-
module \$_DLATCHSR_NNN_ (E, S, R, D, Q);
input E, S, R, D;
output reg Q;
always @* begin
	if (R == 0)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCHSR_NNP_ (E, S, R, D, Q)
//* group reg_latch
//-
//- A negative enable D-type latch with negative polarity set and positive
//- polarity reset.
//-
//- Truth table:    E S R D | Q
//-                ---------+---
//-                 - - 1 - | 0
//-                 - 0 - - | 1
//-                 0 - - d | d
//-                 - - - - | q
//-
module \$_DLATCHSR_NNP_ (E, S, R, D, Q);
input E, S, R, D;
output reg Q;
always @* begin
	if (R == 1)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCHSR_NPN_ (E, S, R, D, Q)
//* group reg_latch
//-
//- A negative enable D-type latch with positive polarity set and negative
//- polarity reset.
//-
//- Truth table:    E S R D | Q
//-                ---------+---
//-                 - - 0 - | 0
//-                 - 1 - - | 1
//-                 0 - - d | d
//-                 - - - - | q
//-
module \$_DLATCHSR_NPN_ (E, S, R, D, Q);
input E, S, R, D;
output reg Q;
always @* begin
	if (R == 0)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCHSR_NPP_ (E, S, R, D, Q)
//* group reg_latch
//-
//- A negative enable D-type latch with positive polarity set and positive
//- polarity reset.
//-
//- Truth table:    E S R D | Q
//-                ---------+---
//-                 - - 1 - | 0
//-                 - 1 - - | 1
//-                 0 - - d | d
//-                 - - - - | q
//-
module \$_DLATCHSR_NPP_ (E, S, R, D, Q);
input E, S, R, D;
output reg Q;
always @* begin
	if (R == 1)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
	else if (E == 0)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCHSR_PNN_ (E, S, R, D, Q)
//* group reg_latch
//-
//- A positive enable D-type latch with negative polarity set and negative
//- polarity reset.
//-
//- Truth table:    E S R D | Q
//-                ---------+---
//-                 - - 0 - | 0
//-                 - 0 - - | 1
//-                 1 - - d | d
//-                 - - - - | q
//-
module \$_DLATCHSR_PNN_ (E, S, R, D, Q);
input E, S, R, D;
output reg Q;
always @* begin
	if (R == 0)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCHSR_PNP_ (E, S, R, D, Q)
//* group reg_latch
//-
//- A positive enable D-type latch with negative polarity set and positive
//- polarity reset.
//-
//- Truth table:    E S R D | Q
//-                ---------+---
//-                 - - 1 - | 0
//-                 - 0 - - | 1
//-                 1 - - d | d
//-                 - - - - | q
//-
module \$_DLATCHSR_PNP_ (E, S, R, D, Q);
input E, S, R, D;
output reg Q;
always @* begin
	if (R == 1)
		Q <= 0;
	else if (S == 0)
		Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCHSR_PPN_ (E, S, R, D, Q)
//* group reg_latch
//-
//- A positive enable D-type latch with positive polarity set and negative
//- polarity reset.
//-
//- Truth table:    E S R D | Q
//-                ---------+---
//-                 - - 0 - | 0
//-                 - 1 - - | 1
//-                 1 - - d | d
//-                 - - - - | q
//-
module \$_DLATCHSR_PPN_ (E, S, R, D, Q);
input E, S, R, D;
output reg Q;
always @* begin
	if (R == 0)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule

//  |---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|---v---|
//-
//-     $_DLATCHSR_PPP_ (E, S, R, D, Q)
//* group reg_latch
//-
//- A positive enable D-type latch with positive polarity set and positive
//- polarity reset.
//-
//- Truth table:    E S R D | Q
//-                ---------+---
//-                 - - 1 - | 0
//-                 - 1 - - | 1
//-                 1 - - d | d
//-                 - - - - | q
//-
module \$_DLATCHSR_PPP_ (E, S, R, D, Q);
input E, S, R, D;
output reg Q;
always @* begin
	if (R == 1)
		Q <= 0;
	else if (S == 1)
		Q <= 1;
	else if (E == 1)
		Q <= D;
end
endmodule
