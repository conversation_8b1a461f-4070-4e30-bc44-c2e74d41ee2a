
module $__NX_RFB_U_DPREG_ (
  input PORT_W_CLK,
  input [6-1:0] PORT_W_ADDR,
  input [6-1:0] PORT_R_ADDR,
  input [36-1:0] PORT_W_WR_DATA,
  input PORT_W_WR_EN,
  output [36-1:0] PORT_R_RD_DATA
);
  parameter INIT = 1152'bx;
  parameter PORT_W_CLK_POL = 1'b1;
  parameter OPTION_MODE = 0;
  parameter WIDTH = 18;
  parameter BITS_USED = 0;
  localparam BLOCK_NUM  = OPTION_MODE == 2 ? 64 : 32;
  localparam BLOCK_SIZE = OPTION_MODE == 3 ? 36 : 18;

`include "rf_init.vh"

  // mode 0 - DPREG
  // mode 2 - NX_XRFB_64x18
  // mode 3 - NX_XRFB_32x36
  NX_RFB_U #(
    .mode(OPTION_MODE),
    .mem_ctxt($sformatf("%s",rf_init_to_string(INIT, BLOCK_NUM, BLOCK_SIZE))),
    .wck_edge(PORT_W_CLK_POL == 1 ? 1'b0 : 1'b1)
  ) _TECHMAP_REPLACE_ (
    .WCK(PORT_W_CLK),
    .I1(PORT_W_WR_DATA[0]),
    .I2(PORT_W_WR_DATA[1]),
    .I3(PORT_W_WR_DATA[2]),
    .I4(PORT_W_WR_DATA[3]),
    .I5(PORT_W_WR_DATA[4]),
    .I6(PORT_W_WR_DATA[5]),
    .I7(PORT_W_WR_DATA[6]),
    .I8(PORT_W_WR_DATA[7]),
    .I9(PORT_W_WR_DATA[8]),
    .I10(PORT_W_WR_DATA[9]),
    .I11(PORT_W_WR_DATA[10]),
    .I12(PORT_W_WR_DATA[11]),
    .I13(PORT_W_WR_DATA[12]),
    .I14(PORT_W_WR_DATA[13]),
    .I15(PORT_W_WR_DATA[14]),
    .I16(PORT_W_WR_DATA[15]),
    .I17(PORT_W_WR_DATA[16]),
    .I18(PORT_W_WR_DATA[17]),
    .I19(PORT_W_WR_DATA[18]),
    .I20(PORT_W_WR_DATA[19]),
    .I21(PORT_W_WR_DATA[20]),
    .I22(PORT_W_WR_DATA[21]),
    .I23(PORT_W_WR_DATA[22]),
    .I24(PORT_W_WR_DATA[23]),
    .I25(PORT_W_WR_DATA[24]),
    .I26(PORT_W_WR_DATA[25]),
    .I27(PORT_W_WR_DATA[26]),
    .I28(PORT_W_WR_DATA[27]),
    .I29(PORT_W_WR_DATA[28]),
    .I30(PORT_W_WR_DATA[29]),
    .I31(PORT_W_WR_DATA[30]),
    .I32(PORT_W_WR_DATA[31]),
    .I33(PORT_W_WR_DATA[32]),
    .I34(PORT_W_WR_DATA[33]),
    .I35(PORT_W_WR_DATA[34]),
    .I36(PORT_W_WR_DATA[35]),
    .O1(PORT_R_RD_DATA[0]),
    .O2(PORT_R_RD_DATA[1]),
    .O3(PORT_R_RD_DATA[2]),
    .O4(PORT_R_RD_DATA[3]),
    .O5(PORT_R_RD_DATA[4]),
    .O6(PORT_R_RD_DATA[5]),
    .O7(PORT_R_RD_DATA[6]),
    .O8(PORT_R_RD_DATA[7]),
    .O9(PORT_R_RD_DATA[8]),
    .O10(PORT_R_RD_DATA[9]),
    .O11(PORT_R_RD_DATA[10]),
    .O12(PORT_R_RD_DATA[11]),
    .O13(PORT_R_RD_DATA[12]),
    .O14(PORT_R_RD_DATA[13]),
    .O15(PORT_R_RD_DATA[14]),
    .O16(PORT_R_RD_DATA[15]),
    .O17(PORT_R_RD_DATA[16]),
    .O18(PORT_R_RD_DATA[17]),
    .O19(PORT_R_RD_DATA[18]),
    .O20(PORT_R_RD_DATA[19]),
    .O21(PORT_R_RD_DATA[20]),
    .O22(PORT_R_RD_DATA[21]),
    .O23(PORT_R_RD_DATA[22]),
    .O24(PORT_R_RD_DATA[23]),
    .O25(PORT_R_RD_DATA[24]),
    .O26(PORT_R_RD_DATA[25]),
    .O27(PORT_R_RD_DATA[26]),
    .O28(PORT_R_RD_DATA[27]),
    .O29(PORT_R_RD_DATA[28]),
    .O30(PORT_R_RD_DATA[29]),
    .O31(PORT_R_RD_DATA[30]),
    .O32(PORT_R_RD_DATA[31]),
    .O33(PORT_R_RD_DATA[32]),
    .O34(PORT_R_RD_DATA[33]),
    .O35(PORT_R_RD_DATA[34]),
    .O36(PORT_R_RD_DATA[35]),
    .RA1(PORT_R_ADDR[0]),
    .RA2(PORT_R_ADDR[1]),
    .RA3(PORT_R_ADDR[2]),
    .RA4(PORT_R_ADDR[3]),
    .RA5(PORT_R_ADDR[4]),
    .RA6(PORT_R_ADDR[5]),
    .RA7(),
    .RA8(),
    .RA9(),
    .RA10(),
    .WA1(PORT_W_ADDR[0]),
    .WA2(PORT_W_ADDR[1]),
    .WA3(PORT_W_ADDR[2]),
    .WA4(PORT_W_ADDR[3]),
    .WA5(PORT_W_ADDR[4]),
    .WA6(PORT_W_ADDR[5]),
    .WE(PORT_W_WR_EN),
    .WEA(1'b0)
  );
endmodule

module $__NX_RFB_U_SPREG_ (
  input PORT_RW_CLK,
  input [4:0] PORT_RW_ADDR,
  input [17:0] PORT_RW_WR_DATA,
  input PORT_RW_WR_EN,
  output [17:0] PORT_RW_RD_DATA
);
  parameter INIT = 576'bx;
  parameter PORT_RW_CLK_POL = 1'b1;
  parameter BITS_USED = 0;
`include "rf_init.vh"

  NX_RFB_U #(
    .mode(1),
    .mem_ctxt($sformatf("%s",rf_init_to_string(INIT, 32, 18))),
    .wck_edge(PORT_RW_CLK_POL == 1 ? 1'b0 : 1'b1)
  ) _TECHMAP_REPLACE_ (
    .WCK(PORT_RW_CLK),
    .I1(PORT_RW_WR_DATA[0]),
    .I2(PORT_RW_WR_DATA[1]),
    .I3(PORT_RW_WR_DATA[2]),
    .I4(PORT_RW_WR_DATA[3]),
    .I5(PORT_RW_WR_DATA[4]),
    .I6(PORT_RW_WR_DATA[5]),
    .I7(PORT_RW_WR_DATA[6]),
    .I8(PORT_RW_WR_DATA[7]),
    .I9(PORT_RW_WR_DATA[8]),
    .I10(PORT_RW_WR_DATA[9]),
    .I11(PORT_RW_WR_DATA[10]),
    .I12(PORT_RW_WR_DATA[11]),
    .I13(PORT_RW_WR_DATA[12]),
    .I14(PORT_RW_WR_DATA[13]),
    .I15(PORT_RW_WR_DATA[14]),
    .I16(PORT_RW_WR_DATA[15]),
    .I17(PORT_RW_WR_DATA[16]),
    .I18(PORT_RW_WR_DATA[17]),
    .I19(),
    .I20(),
    .I21(),
    .I22(),
    .I23(),
    .I24(),
    .I25(),
    .I26(),
    .I27(),
    .I28(),
    .I29(),
    .I30(),
    .I31(),
    .I32(),
    .I33(),
    .I34(),
    .I35(),
    .I36(),
    .O1(PORT_RW_RD_DATA[0]),
    .O2(PORT_RW_RD_DATA[1]),
    .O3(PORT_RW_RD_DATA[2]),
    .O4(PORT_RW_RD_DATA[3]),
    .O5(PORT_RW_RD_DATA[4]),
    .O6(PORT_RW_RD_DATA[5]),
    .O7(PORT_RW_RD_DATA[6]),
    .O8(PORT_RW_RD_DATA[7]),
    .O9(PORT_RW_RD_DATA[8]),
    .O10(PORT_RW_RD_DATA[9]),
    .O11(PORT_RW_RD_DATA[10]),
    .O12(PORT_RW_RD_DATA[11]),
    .O13(PORT_RW_RD_DATA[12]),
    .O14(PORT_RW_RD_DATA[13]),
    .O15(PORT_RW_RD_DATA[14]),
    .O16(PORT_RW_RD_DATA[15]),
    .O17(PORT_RW_RD_DATA[16]),
    .O18(PORT_RW_RD_DATA[17]),
    .O19(),
    .O20(),
    .O21(),
    .O22(),
    .O23(),
    .O24(),
    .O25(),
    .O26(),
    .O27(),
    .O28(),
    .O29(),
    .O30(),
    .O31(),
    .O32(),
    .O33(),
    .O34(),
    .O35(),
    .O36(),
    .RA1(),
    .RA2(),
    .RA3(),
    .RA4(),
    .RA5(),
    .RA6(),
    .RA7(),
    .RA8(),
    .RA9(),
    .RA10(),
    .WA1(PORT_RW_ADDR[0]),
    .WA2(PORT_RW_ADDR[1]),
    .WA3(PORT_RW_ADDR[2]),
    .WA4(PORT_RW_ADDR[3]),
    .WA5(PORT_RW_ADDR[4]),
    .WA6(),
    .WE(PORT_RW_WR_EN),
    .WEA(1'b0)
  );
endmodule

module $__NX_XRFB_2R_1W_ (
  input PORT_W_CLK,
  input [4:0] PORT_W_ADDR,
  input [4:0] PORT_A_ADDR,
  input [4:0] PORT_B_ADDR,
  input [17:0] PORT_W_WR_DATA,
  input PORT_W_WR_EN,
  output [17:0] PORT_A_RD_DATA,
  output [17:0] PORT_B_RD_DATA
);
  parameter INIT = 576'bx;
  parameter PORT_W_CLK_POL = 1'b1;
  parameter BITS_USED = 0;
`include "rf_init.vh"

  NX_RFB_U #(
    .mode(4),
    .mem_ctxt($sformatf("%s",rf_init_to_string(INIT, 32, 18))),
    .wck_edge(PORT_W_CLK_POL == 1 ? 1'b0 : 1'b1)
  ) _TECHMAP_REPLACE_ (
    .WCK(PORT_W_CLK),
    .I1(PORT_W_WR_DATA[0]),
    .I2(PORT_W_WR_DATA[1]),
    .I3(PORT_W_WR_DATA[2]),
    .I4(PORT_W_WR_DATA[3]),
    .I5(PORT_W_WR_DATA[4]),
    .I6(PORT_W_WR_DATA[5]),
    .I7(PORT_W_WR_DATA[6]),
    .I8(PORT_W_WR_DATA[7]),
    .I9(PORT_W_WR_DATA[8]),
    .I10(PORT_W_WR_DATA[9]),
    .I11(PORT_W_WR_DATA[10]),
    .I12(PORT_W_WR_DATA[11]),
    .I13(PORT_W_WR_DATA[12]),
    .I14(PORT_W_WR_DATA[13]),
    .I15(PORT_W_WR_DATA[14]),
    .I16(PORT_W_WR_DATA[15]),
    .I17(PORT_W_WR_DATA[16]),
    .I18(PORT_W_WR_DATA[17]),
    .I19(),
    .I20(),
    .I21(),
    .I22(),
    .I23(),
    .I24(),
    .I25(),
    .I26(),
    .I27(),
    .I28(),
    .I29(),
    .I30(),
    .I31(),
    .I32(),
    .I33(),
    .I34(),
    .I35(),
    .I36(),
    .O1(PORT_A_RD_DATA[0]),
    .O2(PORT_A_RD_DATA[1]),
    .O3(PORT_A_RD_DATA[2]),
    .O4(PORT_A_RD_DATA[3]),
    .O5(PORT_A_RD_DATA[4]),
    .O6(PORT_A_RD_DATA[5]),
    .O7(PORT_A_RD_DATA[6]),
    .O8(PORT_A_RD_DATA[7]),
    .O9(PORT_A_RD_DATA[8]),
    .O10(PORT_A_RD_DATA[9]),
    .O11(PORT_A_RD_DATA[10]),
    .O12(PORT_A_RD_DATA[11]),
    .O13(PORT_A_RD_DATA[12]),
    .O14(PORT_A_RD_DATA[13]),
    .O15(PORT_A_RD_DATA[14]),
    .O16(PORT_A_RD_DATA[15]),
    .O17(PORT_A_RD_DATA[16]),
    .O18(PORT_A_RD_DATA[17]),
    .O19(PORT_B_RD_DATA[0]),
    .O20(PORT_B_RD_DATA[1]),
    .O21(PORT_B_RD_DATA[2]),
    .O22(PORT_B_RD_DATA[3]),
    .O23(PORT_B_RD_DATA[4]),
    .O24(PORT_B_RD_DATA[5]),
    .O25(PORT_B_RD_DATA[6]),
    .O26(PORT_B_RD_DATA[7]),
    .O27(PORT_B_RD_DATA[8]),
    .O28(PORT_B_RD_DATA[9]),
    .O29(PORT_B_RD_DATA[10]),
    .O30(PORT_B_RD_DATA[11]),
    .O31(PORT_B_RD_DATA[12]),
    .O32(PORT_B_RD_DATA[13]),
    .O33(PORT_B_RD_DATA[14]),
    .O34(PORT_B_RD_DATA[15]),
    .O35(PORT_B_RD_DATA[16]),
    .O36(PORT_B_RD_DATA[17]),
    .RA1(PORT_A_ADDR[0]),
    .RA2(PORT_A_ADDR[1]),
    .RA3(PORT_A_ADDR[2]),
    .RA4(PORT_A_ADDR[3]),
    .RA5(PORT_A_ADDR[4]),
    .RA6(PORT_B_ADDR[0]),
    .RA7(PORT_B_ADDR[1]),
    .RA8(PORT_B_ADDR[2]),
    .RA9(PORT_B_ADDR[3]),
    .RA10(PORT_B_ADDR[4]),
    .WA1(PORT_W_ADDR[0]),
    .WA2(PORT_W_ADDR[1]),
    .WA3(PORT_W_ADDR[2]),
    .WA4(PORT_W_ADDR[3]),
    .WA5(PORT_W_ADDR[4]),
    .WA6(),
    .WE(PORT_W_WR_EN),
    .WEA(1'b0)
  );
endmodule