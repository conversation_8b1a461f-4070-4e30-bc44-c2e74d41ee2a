(* blackbox *)
module NX_CKS(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>);
    input CKI;
    output CKO;
    input CMD;
    parameter ck_edge = 1'b0;
endmodule

(* blackbox *)
module NX_CDC_L(CK1, CK2, AI1, AI2, AI3, AI4, AI5, AI6, AO1, AO2, AO3, AO4, AO5, AO6, BI1, BI2, BI3, BI4, BI5, BI6, BO1
, BO2, BO3, BO4, BO5, BO6, CI1, CI2, CI3, CI4, CI5, CI6, CO1, CO2, CO3, CO4, CO5, CO6, DI1, DI2, DI3, DI4
, DI5, DI6, DO1, DO2, DO3, DO4, DO5, DO6);
    input AI1;
    input AI2;
    input AI3;
    input AI4;
    input AI5;
    input AI6;
    output AO1;
    output AO2;
    output AO3;
    output AO4;
    output AO5;
    output AO6;
    input BI1;
    input BI2;
    input BI3;
    input BI4;
    input BI5;
    input BI6;
    output BO1;
    output BO2;
    output BO3;
    output BO4;
    output BO5;
    output BO6;
    input CI1;
    input CI2;
    input CI3;
    input CI4;
    input CI5;
    input CI6;
    input CK1;
    input CK2;
    output CO1;
    output CO2;
    output CO3;
    output CO4;
    output CO5;
    output CO6;
    input DI1;
    input DI2;
    input DI3;
    input DI4;
    input DI5;
    input DI6;
    output DO1;
    output DO2;
    output DO3;
    output DO4;
    output DO5;
    output DO6;
    parameter ack_sel = 1'b0;
    parameter bck_sel = 1'b0;
    parameter cck_sel = 1'b0;
    parameter ck0_edge = 1'b0;
    parameter ck1_edge = 1'b0;
    parameter dck_sel = 1'b0;
    parameter gt0_bypass_reg1 = 1'b0;
    parameter gt0_bypass_reg2 = 1'b0;
    parameter gt1_bypass_reg1 = 1'b0;
    parameter gt1_bypass_reg2 = 1'b0;
    parameter link_BA = 1'b0;
    parameter link_CB = 1'b0;
    parameter link_DC = 1'b0;
    parameter mode = 0;
    parameter use_adest_arst = 2'b00;
    parameter use_asrc_arst = 2'b00;
    parameter use_bdest_arst = 2'b00;
    parameter use_bsrc_arst = 2'b00;
    parameter use_cdest_arst = 2'b00;
    parameter use_csrc_arst = 2'b00;
    parameter use_ddest_arst = 2'b00;
    parameter use_dsrc_arst = 2'b00;
endmodule

(* blackbox *)
module NX_DSP_L(A1, A2, A3, A4, A5, A6, A7, A8, A9, A10, A11, A12, A13, A14, A15, A16, A17, A18, A19, A20, A21
, A22, A23, A24, B1, B2, B3, B4, B5, B6, B7, B8, B9, B10, B11, B12, B13, B14, B15, B16, B17, B18
, C1, C2, C3, C4, C5, C6, C7, C8, C9, C10, C11, C12, C13, C14, C15, C16, C17, C18, C19, C20, C21
, C22, C23, C24, C25, C26, C27, C28, C29, C30, C31, C32, C33, C34, C35, C36, CAI1, CAI2, CAI3, CAI4, CAI5, CAI6
, CAI7, CAI8, CAI9, CAI10, CAI11, CAI12, CAI13, CAI14, CAI15, CAI16, CAI17, CAI18, CAI19, CAI20, CAI21, CAI22, CAI23, CAI24, CAO1, CAO2, CAO3
, CAO4, CAO5, CAO6, CAO7, CAO8, CAO9, CAO10, CAO11, CAO12, CAO13, CAO14, CAO15, CAO16, CAO17, CAO18, CAO19, CAO20, CAO21, CAO22, CAO23, CAO24
, CBI1, CBI2, CBI3, CBI4, CBI5, CBI6, CBI7, CBI8, CBI9, CBI10, CBI11, CBI12, CBI13, CBI14, CBI15, CBI16, CBI17, CBI18, CBO1, CBO2, CBO3
, CBO4, CBO5, CBO6, CBO7, CBO8, CBO9, CBO10, CBO11, CBO12, CBO13, CBO14, CBO15, CBO16, CBO17, CBO18, CCI, CCO, CI, CK, CO, CO37
, CO57, CZI1, CZI2, CZI3, CZI4, CZI5, CZI6, CZI7, CZI8, CZI9, CZI10, CZI11, CZI12, CZI13, CZI14, CZI15, CZI16, CZI17, CZI18, CZI19, CZI20
, CZI21, CZI22, CZI23, CZI24, CZI25, CZI26, CZI27, CZI28, CZI29, CZI30, CZI31, CZI32, CZI33, CZI34, CZI35, CZI36, CZI37, CZI38, CZI39, CZI40, CZI41
, CZI42, CZI43, CZI44, CZI45, CZI46, CZI47, CZI48, CZI49, CZI50, CZI51, CZI52, CZI53, CZI54, CZI55, CZI56, CZO1, CZO2, CZO3, CZO4, CZO5, CZO6
, CZO7, CZO8, CZO9, CZO10, CZO11, CZO12, CZO13, CZO14, CZO15, CZO16, CZO17, CZO18, CZO19, CZO20, CZO21, CZO22, CZO23, CZO24, CZO25, CZO26, CZO27
, CZO28, CZO29, CZO30, CZO31, CZO32, CZO33, CZO34, CZO35, CZO36, CZO37, CZO38, CZO39, CZO40, CZO41, CZO42, CZO43, CZO44, CZO45, CZO46, CZO47, CZO48
, CZO49, CZO50, CZO51, CZO52, CZO53, CZO54, CZO55, CZO56, D1, D2, D3, D4, D5, D6, D7, D8, D9, D10, D11, D12, D13
, D14, D15, D16, D17, D18, OVF, R, RZ, WE, Z1, Z2, Z3, Z4, Z5, Z6, Z7, Z8, Z9, Z10, Z11, Z12
, Z13, Z14, Z15, Z16, Z17, Z18, Z19, Z20, Z21, Z22, Z23, Z24, Z25, Z26, Z27, Z28, Z29, Z30, Z31, Z32, Z33
, Z34, Z35, Z36, Z37, Z38, Z39, Z40, Z41, Z42, Z43, Z44, Z45, Z46, Z47, Z48, Z49, Z50, Z51, Z52, Z53, Z54
, Z55, Z56);
    input A1;
    input A10;
    input A11;
    input A12;
    input A13;
    input A14;
    input A15;
    input A16;
    input A17;
    input A18;
    input A19;
    input A2;
    input A20;
    input A21;
    input A22;
    input A23;
    input A24;
    input A3;
    input A4;
    input A5;
    input A6;
    input A7;
    input A8;
    input A9;
    input B1;
    input B10;
    input B11;
    input B12;
    input B13;
    input B14;
    input B15;
    input B16;
    input B17;
    input B18;
    input B2;
    input B3;
    input B4;
    input B5;
    input B6;
    input B7;
    input B8;
    input B9;
    input C1;
    input C10;
    input C11;
    input C12;
    input C13;
    input C14;
    input C15;
    input C16;
    input C17;
    input C18;
    input C19;
    input C2;
    input C20;
    input C21;
    input C22;
    input C23;
    input C24;
    input C25;
    input C26;
    input C27;
    input C28;
    input C29;
    input C3;
    input C30;
    input C31;
    input C32;
    input C33;
    input C34;
    input C35;
    input C36;
    input C4;
    input C5;
    input C6;
    input C7;
    input C8;
    input C9;
    input CAI1;
    input CAI10;
    input CAI11;
    input CAI12;
    input CAI13;
    input CAI14;
    input CAI15;
    input CAI16;
    input CAI17;
    input CAI18;
    input CAI19;
    input CAI2;
    input CAI20;
    input CAI21;
    input CAI22;
    input CAI23;
    input CAI24;
    input CAI3;
    input CAI4;
    input CAI5;
    input CAI6;
    input CAI7;
    input CAI8;
    input CAI9;
    output CAO1;
    output CAO10;
    output CAO11;
    output CAO12;
    output CAO13;
    output CAO14;
    output CAO15;
    output CAO16;
    output CAO17;
    output CAO18;
    output CAO19;
    output CAO2;
    output CAO20;
    output CAO21;
    output CAO22;
    output CAO23;
    output CAO24;
    output CAO3;
    output CAO4;
    output CAO5;
    output CAO6;
    output CAO7;
    output CAO8;
    output CAO9;
    input CBI1;
    input CBI10;
    input CBI11;
    input CBI12;
    input CBI13;
    input CBI14;
    input CBI15;
    input CBI16;
    input CBI17;
    input CBI18;
    input CBI2;
    input CBI3;
    input CBI4;
    input CBI5;
    input CBI6;
    input CBI7;
    input CBI8;
    input CBI9;
    output CBO1;
    output CBO10;
    output CBO11;
    output CBO12;
    output CBO13;
    output CBO14;
    output CBO15;
    output CBO16;
    output CBO17;
    output CBO18;
    output CBO2;
    output CBO3;
    output CBO4;
    output CBO5;
    output CBO6;
    output CBO7;
    output CBO8;
    output CBO9;
    input CCI;
    output CCO;
    input CI;
    input CK;
    output CO;
    output CO37;
    output CO57;
    input CZI1;
    input CZI10;
    input CZI11;
    input CZI12;
    input CZI13;
    input CZI14;
    input CZI15;
    input CZI16;
    input CZI17;
    input CZI18;
    input CZI19;
    input CZI2;
    input CZI20;
    input CZI21;
    input CZI22;
    input CZI23;
    input CZI24;
    input CZI25;
    input CZI26;
    input CZI27;
    input CZI28;
    input CZI29;
    input CZI3;
    input CZI30;
    input CZI31;
    input CZI32;
    input CZI33;
    input CZI34;
    input CZI35;
    input CZI36;
    input CZI37;
    input CZI38;
    input CZI39;
    input CZI4;
    input CZI40;
    input CZI41;
    input CZI42;
    input CZI43;
    input CZI44;
    input CZI45;
    input CZI46;
    input CZI47;
    input CZI48;
    input CZI49;
    input CZI5;
    input CZI50;
    input CZI51;
    input CZI52;
    input CZI53;
    input CZI54;
    input CZI55;
    input CZI56;
    input CZI6;
    input CZI7;
    input CZI8;
    input CZI9;
    output CZO1;
    output CZO10;
    output CZO11;
    output CZO12;
    output CZO13;
    output CZO14;
    output CZO15;
    output CZO16;
    output CZO17;
    output CZO18;
    output CZO19;
    output CZO2;
    output CZO20;
    output CZO21;
    output CZO22;
    output CZO23;
    output CZO24;
    output CZO25;
    output CZO26;
    output CZO27;
    output CZO28;
    output CZO29;
    output CZO3;
    output CZO30;
    output CZO31;
    output CZO32;
    output CZO33;
    output CZO34;
    output CZO35;
    output CZO36;
    output CZO37;
    output CZO38;
    output CZO39;
    output CZO4;
    output CZO40;
    output CZO41;
    output CZO42;
    output CZO43;
    output CZO44;
    output CZO45;
    output CZO46;
    output CZO47;
    output CZO48;
    output CZO49;
    output CZO5;
    output CZO50;
    output CZO51;
    output CZO52;
    output CZO53;
    output CZO54;
    output CZO55;
    output CZO56;
    output CZO6;
    output CZO7;
    output CZO8;
    output CZO9;
    input D1;
    input D10;
    input D11;
    input D12;
    input D13;
    input D14;
    input D15;
    input D16;
    input D17;
    input D18;
    input D2;
    input D3;
    input D4;
    input D5;
    input D6;
    input D7;
    input D8;
    input D9;
    output OVF;
    input R;
    input RZ;
    input WE;
    output Z1;
    output Z10;
    output Z11;
    output Z12;
    output Z13;
    output Z14;
    output Z15;
    output Z16;
    output Z17;
    output Z18;
    output Z19;
    output Z2;
    output Z20;
    output Z21;
    output Z22;
    output Z23;
    output Z24;
    output Z25;
    output Z26;
    output Z27;
    output Z28;
    output Z29;
    output Z3;
    output Z30;
    output Z31;
    output Z32;
    output Z33;
    output Z34;
    output Z35;
    output Z36;
    output Z37;
    output Z38;
    output Z39;
    output Z4;
    output Z40;
    output Z41;
    output Z42;
    output Z43;
    output Z44;
    output Z45;
    output Z46;
    output Z47;
    output Z48;
    output Z49;
    output Z5;
    output Z50;
    output Z51;
    output Z52;
    output Z53;
    output Z54;
    output Z55;
    output Z56;
    output Z6;
    output Z7;
    output Z8;
    output Z9;
    parameter raw_config0 = 20'b00000000000000000000;
    parameter raw_config1 = 19'b0000000000000000000;
    parameter raw_config2 = 13'b0000000000000;
    parameter raw_config3 = 7'b0000000;
    parameter std_mode = "";
endmodule

(* blackbox *)
module NX_PLL_L(REF, FBK, R, VCO, LDFO, REFO, DIVO1, DIVO2, DIVP1, DIVP2, DIVP3, OSC, PLL_LOCKED, CAL_LOCKED);
    output CAL_LOCKED;
    output DIVO1;
    output DIVO2;
    output DIVP1;
    output DIVP2;
    output DIVP3;
    input FBK;
    output LDFO;
    output OSC;
    output PLL_LOCKED;
    input R;
    input REF;
    output REFO;
    output VCO;
    parameter cfg_use_pll = 1'b1;
    parameter clk_outdivo1 = 0;
    parameter clk_outdivp1 = 0;
    parameter clk_outdivp2 = 0;
    parameter clk_outdivp3o2 = 0;
    parameter ext_fbk_on = 1'b0;
    parameter fbk_delay = 0;
    parameter fbk_delay_on = 1'b0;
    parameter fbk_intdiv = 2;
    parameter location = "";
    parameter pll_cpump = 3'b010;
    parameter ref_intdiv = 0;
    parameter ref_osc_on = 1'b0;
    parameter wfg_sync_cal_lock = 1'b0;
    parameter wfg_sync_pll_lock = 1'b0;
endmodule

(* blackbox *)
module NX_WFG_L(R, SI, ZI, RDY, SO, ZO);
    input R;
    input RDY;
    input SI;
    output SO;
    input ZI;
    output ZO;
    parameter delay = 0;
    parameter delay_on = 1'b0;
    parameter location = "";
    parameter mode = 1'b0;
    parameter pattern = 16'b0000000000000000;
    parameter pattern_end = 1;
    parameter wfg_edge = 1'b0;
endmodule

(* blackbox *)
module NX_CRX_L(DSCR_E_I, DEC_E_I, ALIGN_E_I, ALIGN_S_I, REP_E_I, BUF_R_I, OVS_BS_I1, OVS_BS_I2, BUF_FE_I, RST_N_I, CDR_R_I, CKG_RN_I, PLL_RN_I, TST_I1, TST_I2, TST_I3, TST_I4, LOS_O, DATA_O1, DATA_O2, DATA_O3
, DATA_O4, DATA_O5, DATA_O6, DATA_O7, DATA_O8, DATA_O9, DATA_O10, DATA_O11, DATA_O12, DATA_O13, DATA_O14, DATA_O15, DATA_O16, DATA_O17, DATA_O18, DATA_O19, DATA_O20, DATA_O21, DATA_O22, DATA_O23, DATA_O24
, DATA_O25, DATA_O26, DATA_O27, DATA_O28, DATA_O29, DATA_O30, DATA_O31, DATA_O32, DATA_O33, DATA_O34, DATA_O35, DATA_O36, DATA_O37, DATA_O38, DATA_O39, DATA_O40, DATA_O41, DATA_O42, DATA_O43, DATA_O44, DATA_O45
, DATA_O46, DATA_O47, DATA_O48, DATA_O49, DATA_O50, DATA_O51, DATA_O52, DATA_O53, DATA_O54, DATA_O55, DATA_O56, DATA_O57, DATA_O58, DATA_O59, DATA_O60, DATA_O61, DATA_O62, DATA_O63, DATA_O64, CH_COM_O1, CH_COM_O2
, CH_COM_O3, CH_COM_O4, CH_COM_O5, CH_COM_O6, CH_COM_O7, CH_COM_O8, CH_K_O1, CH_K_O2, CH_K_O3, CH_K_O4, CH_K_O5, CH_K_O6, CH_K_O7, CH_K_O8, NIT_O1, NIT_O2, NIT_O3, NIT_O4, NIT_O5, NIT_O6, NIT_O7
, NIT_O8, D_ERR_O1, D_ERR_O2, D_ERR_O3, D_ERR_O4, D_ERR_O5, D_ERR_O6, D_ERR_O7, D_ERR_O8, CH_A_O1, CH_A_O2, CH_A_O3, CH_A_O4, CH_A_O5, CH_A_O6, CH_A_O7, CH_A_O8, CH_F_O1, CH_F_O2, CH_F_O3, CH_F_O4
, CH_F_O5, CH_F_O6, CH_F_O7, CH_F_O8, ALIGN_O, BUSY_O, TST_O1, TST_O2, TST_O3, TST_O4, TST_O5, TST_O6, TST_O7, TST_O8, LOCK_O, RX_I, LINK);
    input ALIGN_E_I;
    output ALIGN_O;
    input ALIGN_S_I;
    input BUF_FE_I;
    input BUF_R_I;
    output BUSY_O;
    input CDR_R_I;
    output CH_A_O1;
    output CH_A_O2;
    output CH_A_O3;
    output CH_A_O4;
    output CH_A_O5;
    output CH_A_O6;
    output CH_A_O7;
    output CH_A_O8;
    output CH_COM_O1;
    output CH_COM_O2;
    output CH_COM_O3;
    output CH_COM_O4;
    output CH_COM_O5;
    output CH_COM_O6;
    output CH_COM_O7;
    output CH_COM_O8;
    output CH_F_O1;
    output CH_F_O2;
    output CH_F_O3;
    output CH_F_O4;
    output CH_F_O5;
    output CH_F_O6;
    output CH_F_O7;
    output CH_F_O8;
    output CH_K_O1;
    output CH_K_O2;
    output CH_K_O3;
    output CH_K_O4;
    output CH_K_O5;
    output CH_K_O6;
    output CH_K_O7;
    output CH_K_O8;
    input CKG_RN_I;
    output DATA_O1;
    output DATA_O10;
    output DATA_O11;
    output DATA_O12;
    output DATA_O13;
    output DATA_O14;
    output DATA_O15;
    output DATA_O16;
    output DATA_O17;
    output DATA_O18;
    output DATA_O19;
    output DATA_O2;
    output DATA_O20;
    output DATA_O21;
    output DATA_O22;
    output DATA_O23;
    output DATA_O24;
    output DATA_O25;
    output DATA_O26;
    output DATA_O27;
    output DATA_O28;
    output DATA_O29;
    output DATA_O3;
    output DATA_O30;
    output DATA_O31;
    output DATA_O32;
    output DATA_O33;
    output DATA_O34;
    output DATA_O35;
    output DATA_O36;
    output DATA_O37;
    output DATA_O38;
    output DATA_O39;
    output DATA_O4;
    output DATA_O40;
    output DATA_O41;
    output DATA_O42;
    output DATA_O43;
    output DATA_O44;
    output DATA_O45;
    output DATA_O46;
    output DATA_O47;
    output DATA_O48;
    output DATA_O49;
    output DATA_O5;
    output DATA_O50;
    output DATA_O51;
    output DATA_O52;
    output DATA_O53;
    output DATA_O54;
    output DATA_O55;
    output DATA_O56;
    output DATA_O57;
    output DATA_O58;
    output DATA_O59;
    output DATA_O6;
    output DATA_O60;
    output DATA_O61;
    output DATA_O62;
    output DATA_O63;
    output DATA_O64;
    output DATA_O7;
    output DATA_O8;
    output DATA_O9;
    input DEC_E_I;
    input DSCR_E_I;
    output D_ERR_O1;
    output D_ERR_O2;
    output D_ERR_O3;
    output D_ERR_O4;
    output D_ERR_O5;
    output D_ERR_O6;
    output D_ERR_O7;
    output D_ERR_O8;
    inout [9:0] LINK;
    output LOCK_O;
    output LOS_O;
    output NIT_O1;
    output NIT_O2;
    output NIT_O3;
    output NIT_O4;
    output NIT_O5;
    output NIT_O6;
    output NIT_O7;
    output NIT_O8;
    input OVS_BS_I1;
    input OVS_BS_I2;
    input PLL_RN_I;
    input REP_E_I;
    input RST_N_I;
    input RX_I;
    input TST_I1;
    input TST_I2;
    input TST_I3;
    input TST_I4;
    output TST_O1;
    output TST_O2;
    output TST_O3;
    output TST_O4;
    output TST_O5;
    output TST_O6;
    output TST_O7;
    output TST_O8;
    parameter location = "";
    parameter pcs_8b_dscr_sel = 1'b0;
    parameter pcs_align_bypass = 1'b0;
    parameter pcs_buffers_bypass = 1'b0;
    parameter pcs_buffers_use_cdc = 1'b0;
    parameter pcs_bypass_pma_cdc = 1'b0;
    parameter pcs_bypass_usr_cdc = 1'b0;
    parameter pcs_comma_mask = 10'b0000000000;
    parameter pcs_debug_en = 1'b0;
    parameter pcs_dec_bypass = 1'b0;
    parameter pcs_dscr_bypass = 1'b0;
    parameter pcs_el_buff_diff_bef_comp = 3'b000;
    parameter pcs_el_buff_max_comp = 3'b000;
    parameter pcs_el_buff_only_one_skp = 1'b0;
    parameter pcs_el_buff_skp_char_0 = 9'b000000000;
    parameter pcs_el_buff_skp_char_1 = 9'b000000000;
    parameter pcs_el_buff_skp_char_2 = 9'b000000000;
    parameter pcs_el_buff_skp_char_3 = 9'b000000000;
    parameter pcs_el_buff_skp_header_0 = 9'b000000000;
    parameter pcs_el_buff_skp_header_1 = 9'b000000000;
    parameter pcs_el_buff_skp_header_2 = 9'b000000000;
    parameter pcs_el_buff_skp_header_3 = 9'b000000000;
    parameter pcs_el_buff_skp_header_size = 2'b00;
    parameter pcs_el_buff_skp_seq_size = 2'b00;
    parameter pcs_el_buff_underflow_handle = 1'b0;
    parameter pcs_fsm_sel = 2'b00;
    parameter pcs_fsm_watchdog_en = 1'b0;
    parameter pcs_loopback = 1'b0;
    parameter pcs_m_comma_en = 1'b0;
    parameter pcs_m_comma_val = 10'b0000000000;
    parameter pcs_nb_comma_bef_realign = 2'b00;
    parameter pcs_p_comma_en = 1'b0;
    parameter pcs_p_comma_val = 10'b0000000000;
    parameter pcs_polarity = 1'b0;
    parameter pcs_protocol_size = 1'b0;
    parameter pcs_replace_bypass = 1'b0;
    parameter pcs_sync_supported = 1'b0;
    parameter pma_cdr_cp = 4'b0000;
    parameter pma_clk_pos = 1'b0;
    parameter pma_ctrl_term = 6'b000000;
    parameter pma_loopback = 1'b0;
    parameter pma_pll_cpump_n = 3'b000;
    parameter pma_pll_divf = 2'b00;
    parameter pma_pll_divf_en_n = 1'b0;
    parameter pma_pll_divm = 2'b00;
    parameter pma_pll_divm_en_n = 1'b0;
    parameter pma_pll_divn = 1'b0;
    parameter pma_pll_divn_en_n = 1'b0;
    parameter test = 2'b00;
endmodule

(* blackbox *)
module NX_CTX_L(ENC_E_I1, ENC_E_I2, ENC_E_I3, ENC_E_I4, ENC_E_I5, ENC_E_I6, ENC_E_I7, ENC_E_I8, CH_K_I1, CH_K_I2, CH_K_I3, CH_K_I4, CH_K_I5, CH_K_I6, CH_K_I7, CH_K_I8, SCR_E_I1, SCR_E_I2, SCR_E_I3, SCR_E_I4, SCR_E_I5
, SCR_E_I6, SCR_E_I7, SCR_E_I8, EOMF_I1, EOMF_I2, EOMF_I3, EOMF_I4, EOMF_I5, EOMF_I6, EOMF_I7, EOMF_I8, EOF_I1, EOF_I2, EOF_I3, EOF_I4, EOF_I5, EOF_I6, EOF_I7, EOF_I8, REP_E_I, RST_N_I
, TST_I1, TST_I2, TST_I3, TST_I4, DATA_I1, DATA_I2, DATA_I3, DATA_I4, DATA_I5, DATA_I6, DATA_I7, DATA_I8, DATA_I9, DATA_I10, DATA_I11, DATA_I12, DATA_I13, DATA_I14, DATA_I15, DATA_I16, DATA_I17
, DATA_I18, DATA_I19, DATA_I20, DATA_I21, DATA_I22, DATA_I23, DATA_I24, DATA_I25, DATA_I26, DATA_I27, DATA_I28, DATA_I29, DATA_I30, DATA_I31, DATA_I32, DATA_I33, DATA_I34, DATA_I35, DATA_I36, DATA_I37, DATA_I38
, DATA_I39, DATA_I40, DATA_I41, DATA_I42, DATA_I43, DATA_I44, DATA_I45, DATA_I46, DATA_I47, DATA_I48, DATA_I49, DATA_I50, DATA_I51, DATA_I52, DATA_I53, DATA_I54, DATA_I55, DATA_I56, DATA_I57, DATA_I58, DATA_I59
, DATA_I60, DATA_I61, DATA_I62, DATA_I63, DATA_I64, TST_O1, TST_O2, TST_O3, TST_O4, BUSY_O, CLK_E_I, TX_O, LINK);
    output BUSY_O;
    input CH_K_I1;
    input CH_K_I2;
    input CH_K_I3;
    input CH_K_I4;
    input CH_K_I5;
    input CH_K_I6;
    input CH_K_I7;
    input CH_K_I8;
    input CLK_E_I;
    input DATA_I1;
    input DATA_I10;
    input DATA_I11;
    input DATA_I12;
    input DATA_I13;
    input DATA_I14;
    input DATA_I15;
    input DATA_I16;
    input DATA_I17;
    input DATA_I18;
    input DATA_I19;
    input DATA_I2;
    input DATA_I20;
    input DATA_I21;
    input DATA_I22;
    input DATA_I23;
    input DATA_I24;
    input DATA_I25;
    input DATA_I26;
    input DATA_I27;
    input DATA_I28;
    input DATA_I29;
    input DATA_I3;
    input DATA_I30;
    input DATA_I31;
    input DATA_I32;
    input DATA_I33;
    input DATA_I34;
    input DATA_I35;
    input DATA_I36;
    input DATA_I37;
    input DATA_I38;
    input DATA_I39;
    input DATA_I4;
    input DATA_I40;
    input DATA_I41;
    input DATA_I42;
    input DATA_I43;
    input DATA_I44;
    input DATA_I45;
    input DATA_I46;
    input DATA_I47;
    input DATA_I48;
    input DATA_I49;
    input DATA_I5;
    input DATA_I50;
    input DATA_I51;
    input DATA_I52;
    input DATA_I53;
    input DATA_I54;
    input DATA_I55;
    input DATA_I56;
    input DATA_I57;
    input DATA_I58;
    input DATA_I59;
    input DATA_I6;
    input DATA_I60;
    input DATA_I61;
    input DATA_I62;
    input DATA_I63;
    input DATA_I64;
    input DATA_I7;
    input DATA_I8;
    input DATA_I9;
    input ENC_E_I1;
    input ENC_E_I2;
    input ENC_E_I3;
    input ENC_E_I4;
    input ENC_E_I5;
    input ENC_E_I6;
    input ENC_E_I7;
    input ENC_E_I8;
    input EOF_I1;
    input EOF_I2;
    input EOF_I3;
    input EOF_I4;
    input EOF_I5;
    input EOF_I6;
    input EOF_I7;
    input EOF_I8;
    input EOMF_I1;
    input EOMF_I2;
    input EOMF_I3;
    input EOMF_I4;
    input EOMF_I5;
    input EOMF_I6;
    input EOMF_I7;
    input EOMF_I8;
    inout [19:0] LINK;
    input REP_E_I;
    input RST_N_I;
    input SCR_E_I1;
    input SCR_E_I2;
    input SCR_E_I3;
    input SCR_E_I4;
    input SCR_E_I5;
    input SCR_E_I6;
    input SCR_E_I7;
    input SCR_E_I8;
    input TST_I1;
    input TST_I2;
    input TST_I3;
    input TST_I4;
    output TST_O1;
    output TST_O2;
    output TST_O3;
    output TST_O4;
    output TX_O;
    parameter location = "";
    parameter pcs_8b_scr_sel = 1'b0;
    parameter pcs_bypass_pma_cdc = 1'b0;
    parameter pcs_bypass_usr_cdc = 1'b0;
    parameter pcs_enc_bypass = 1'b0;
    parameter pcs_esistream_fsm_en = 1'b0;
    parameter pcs_loopback = 1'b0;
    parameter pcs_polarity = 1'b0;
    parameter pcs_protocol_size = 1'b0;
    parameter pcs_replace_bypass = 1'b0;
    parameter pcs_scr_bypass = 1'b0;
    parameter pcs_scr_init = 17'b00000000000000000;
    parameter pcs_sync_supported = 1'b0;
    parameter pma_clk_pos = 1'b0;
    parameter pma_loopback = 1'b0;
    parameter test = 2'b00;
endmodule

(* blackbox *)
module NX_IOM_L(RTCK1, RRCK1, WTCK1, WRCK1, RTCK2, RRCK2, WTCK2, WRCK2, CTCK, CCK, DCK, C1TW, C1TS, C1RW1, C1RW2, C1RW3, C1RNE, C1RS, C2TW, C2TS, C2RW1
, C2RW2, C2RW3, C2RNE, C2RS, FA1, FA2, FA3, FA4, FA5, FA6, FZ, DC, DRI1, DRI2, DRI3, DRI4, DRI5, DRI6, DRA1, DRA2, DRA3
, DRA4, DRA5, DRA6, DRL, DOS, DOG, DIS, DIG, DPAS, DPAG, DQSS, DQSG, DS1, DS2, CAD1, CAD2, CAD3, CAD4, CAD5, CAD6, CAP1
, CAP2, CAP3, CAP4, CAN1, CAN2, CAN3, CAN4, CAT1, CAT2, CAT3, CAT4, CKO1, CKO2, FLD, FLG, C1RED, C2RED, DRO1, DRO2, DRO3, DRO4
, DRO5, DRO6, CAL, P1CI1, P1CL, P1CR, P1CO, P1CTI, P1CTO, P1EI1, P1EI2, P1EI3, P1EI4, P1EI5, P1EL, P1ER, P1EO, P1RI, P1RL, P1RR, P1RO1
, P1RO2, P1RO3, P1RO4, P1RO5, P2CI1, P2CL, P2CR, P2CO, P2CTI, P2CTO, P2EI1, P2EI2, P2EI3, P2EI4, P2EI5, P2EL, P2ER, P2EO, P2RI, P2RL, P2RR
, P2RO1, P2RO2, P2RO3, P2RO4, P2RO5, P3CI1, P3CL, P3CR, P3CO, P3CTI, P3CTO, P3EI1, P3EI2, P3EI3, P3EI4, P3EI5, P3EL, P3ER, P3EO, P3RI, P3RL
, P3RR, P3RO1, P3RO2, P3RO3, P3RO4, P3RO5, P4CI1, P4CL, P4CR, P4CO, P4CTI, P4CTO, P4EI1, P4EI2, P4EI3, P4EI4, P4EI5, P4EL, P4ER, P4EO, P4RI
, P4RL, P4RR, P4RO1, P4RO2, P4RO3, P4RO4, P4RO5, P5CI1, P5CI2, P5CI3, P5CI4, P5CI5, P5CL, P5CR, P5CO, P5CTI, P5CTO, P5EI1, P5EI2, P5EI3, P5EI4
, P5EI5, P5EL, P5ER, P5EO, P5RI, P5RL, P5RR, P5RO1, P5RO2, P5RO3, P5RO4, P5RO5, P6CI1, P6CL, P6CR, P6CO, P6CTI, P6CTO, P6EI1, P6EI2, P6EI3
, P6EI4, P6EI5, P6EL, P6ER, P6EO, P6RI, P6RL, P6RR, P6RO1, P6RO2, P6RO3, P6RO4, P6RO5, P7CI1, P7CL, P7CR, P7CO, P7CTI, P7CTO, P7EI1, P7EI2
, P7EI3, P7EI4, P7EI5, P7EL, P7ER, P7EO, P7RI, P7RL, P7RR, P7RO1, P7RO2, P7RO3, P7RO4, P7RO5, P8CI1, P8CL, P8CR, P8CO, P8CTI, P8CTO, P8EI1
, P8EI2, P8EI3, P8EI4, P8EI5, P8EL, P8ER, P8EO, P8RI, P8RL, P8RR, P8RO1, P8RO2, P8RO3, P8RO4, P8RO5, P9CI1, P9CL, P9CR, P9CO, P9CTI, P9CTO
, P9EI1, P9EI2, P9EI3, P9EI4, P9EI5, P9EL, P9ER, P9EO, P9RI, P9RL, P9RR, P9RO1, P9RO2, P9RO3, P9RO4, P9RO5, P10CI1, P10CL, P10CR, P10CO, P10CTI
, P10CTO, P10EI1, P10EI2, P10EI3, P10EI4, P10EI5, P10EL, P10ER, P10EO, P10RI, P10RL, P10RR, P10RO1, P10RO2, P10RO3, P10RO4, P10RO5, P11CI1, P11CL, P11CR, P11CO
, P11CTI, P11CTO, P11EI1, P11EI2, P11EI3, P11EI4, P11EI5, P11EL, P11ER, P11EO, P11RI, P11RL, P11RR, P11RO1, P11RO2, P11RO3, P11RO4, P11RO5, P12CI1, P12CL, P12CR
, P12CO, P12CTI, P12CTO, P12EI1, P12EI2, P12EI3, P12EI4, P12EI5, P12EL, P12ER, P12EO, P12RI, P12RL, P12RR, P12RO1, P12RO2, P12RO3, P12RO4, P12RO5, P13CI1, P13CL
, P13CR, P13CO, P13CTI, P13CTO, P13EI1, P13EI2, P13EI3, P13EI4, P13EI5, P13EL, P13ER, P13EO, P13RI, P13RL, P13RR, P13RO1, P13RO2, P13RO3, P13RO4, P13RO5, P14CI1
, P14CL, P14CR, P14CO, P14CTI, P14CTO, P14EI1, P14EI2, P14EI3, P14EI4, P14EI5, P14EL, P14ER, P14EO, P14RI, P14RL, P14RR, P14RO1, P14RO2, P14RO3, P14RO4, P14RO5
, P15CI1, P15CL, P15CR, P15CO, P15CTI, P15CTO, P15EI1, P15EI2, P15EI3, P15EI4, P15EI5, P15EL, P15ER, P15EO, P15RI, P15RL, P15RR, P15RO1, P15RO2, P15RO3, P15RO4
, P15RO5, P16CI1, P16CL, P16CR, P16CO, P16CTI, P16CTO, P16EI1, P16EI2, P16EI3, P16EI4, P16EI5, P16EL, P16ER, P16EO, P16RI, P16RL, P16RR, P16RO1, P16RO2, P16RO3
, P16RO4, P16RO5, P17CI1, P17CL, P17CR, P17CO, P17CTI, P17CTO, P17EI1, P17EI2, P17EI3, P17EI4, P17EI5, P17EL, P17ER, P17EO, P17RI, P17RL, P17RR, P17RO1, P17RO2
, P17RO3, P17RO4, P17RO5, P18CI1, P18CL, P18CR, P18CO, P18CTI, P18CTO, P18EI1, P18EI2, P18EI3, P18EI4, P18EI5, P18EL, P18ER, P18EO, P18RI, P18RL, P18RR, P18RO1
, P18RO2, P18RO3, P18RO4, P18RO5, P19CI1, P19CL, P19CR, P19CO, P19CTI, P19CTO, P19EI1, P19EI2, P19EI3, P19EI4, P19EI5, P19EL, P19ER, P19EO, P19RI, P19RL, P19RR
, P19RO1, P19RO2, P19RO3, P19RO4, P19RO5, P20CI1, P20CL, P20CR, P20CO, P20CTI, P20CTO, P20EI1, P20EI2, P20EI3, P20EI4, P20EI5, P20EL, P20ER, P20EO, P20RI, P20RL
, P20RR, P20RO1, P20RO2, P20RO3, P20RO4, P20RO5, P21CI1, P21CL, P21CR, P21CO, P21CTI, P21CTO, P21EI1, P21EI2, P21EI3, P21EI4, P21EI5, P21EL, P21ER, P21EO, P21RI
, P21RL, P21RR, P21RO1, P21RO2, P21RO3, P21RO4, P21RO5, P22CI1, P22CL, P22CR, P22CO, P22CTI, P22CTO, P22EI1, P22EI2, P22EI3, P22EI4, P22EI5, P22EL, P22ER, P22EO
, P22RI, P22RL, P22RR, P22RO1, P22RO2, P22RO3, P22RO4, P22RO5, P23CI1, P23CL, P23CR, P23CO, P23CTI, P23CTO, P23EI1, P23EI2, P23EI3, P23EI4, P23EI5, P23EL, P23ER
, P23EO, P23RI, P23RL, P23RR, P23RO1, P23RO2, P23RO3, P23RO4, P23RO5, P24CI1, P24CL, P24CR, P24CO, P24CTI, P24CTO, P24EI1, P24EI2, P24EI3, P24EI4, P24EI5, P24EL
, P24ER, P24EO, P24RI, P24RL, P24RR, P24RO1, P24RO2, P24RO3, P24RO4, P24RO5, P25CI1, P25CL, P25CR, P25CO, P25CTI, P25CTO, P25EI1, P25EI2, P25EI3, P25EI4, P25EI5
, P25EL, P25ER, P25EO, P25RI, P25RL, P25RR, P25RO1, P25RO2, P25RO3, P25RO4, P25RO5, P26CI1, P26CL, P26CR, P26CO, P26CTI, P26CTO, P26EI1, P26EI2, P26EI3, P26EI4
, P26EI5, P26EL, P26ER, P26EO, P26RI, P26RL, P26RR, P26RO1, P26RO2, P26RO3, P26RO4, P26RO5, P27CI1, P27CL, P27CR, P27CO, P27CTI, P27CTO, P27EI1, P27EI2, P27EI3
, P27EI4, P27EI5, P27EL, P27ER, P27EO, P27RI, P27RL, P27RR, P27RO1, P27RO2, P27RO3, P27RO4, P27RO5, P28CI1, P28CL, P28CR, P28CO, P28CTI, P28CTO, P28EI1, P28EI2
, P28EI3, P28EI4, P28EI5, P28EL, P28ER, P28EO, P28RI, P28RL, P28RR, P28RO1, P28RO2, P28RO3, P28RO4, P28RO5, P29CI1, P29CI2, P29CI3, P29CI4, P29CI5, P29CL, P29CR
, P29CO, P29CTI, P29CTO, P29EI1, P29EI2, P29EI3, P29EI4, P29EI5, P29EL, P29ER, P29EO, P29RI, P29RL, P29RR, P29RO1, P29RO2, P29RO3, P29RO4, P29RO5, P30CI1, P30CL
, P30CR, P30CO, P30CTI, P30CTO, P30EI1, P30EI2, P30EI3, P30EI4, P30EI5, P30EL, P30ER, P30EO, P30RI, P30RL, P30RR, P30RO1, P30RO2, P30RO3, P30RO4, P30RO5, P31CI1
, P31CL, P31CR, P31CO, P31CTI, P31CTO, P31EI1, P31EI2, P31EI3, P31EI4, P31EI5, P31EL, P31ER, P31EO, P31RI, P31RL, P31RR, P31RO1, P31RO2, P31RO3, P31RO4, P31RO5
, P32CI1, P32CL, P32CR, P32CO, P32CTI, P32CTO, P32EI1, P32EI2, P32EI3, P32EI4, P32EI5, P32EL, P32ER, P32EO, P32RI, P32RL, P32RR, P32RO1, P32RO2, P32RO3, P32RO4
, P32RO5, P33CI1, P33CL, P33CR, P33CO, P33CTI, P33CTO, P33EI1, P33EI2, P33EI3, P33EI4, P33EI5, P33EL, P33ER, P33EO, P33RI, P33RL, P33RR, P33RO1, P33RO2, P33RO3
, P33RO4, P33RO5, P34CI1, P34CL, P34CR, P34CO, P34CTI, P34CTO, P34EI1, P34EI2, P34EI3, P34EI4, P34EI5, P34EL, P34ER, P34EO, P34RI, P34RL, P34RR, P34RO1, P34RO2
, P34RO3, P34RO4, P34RO5);
    output C1RED;
    input C1RNE;
    input C1RS;
    input C1RW1;
    input C1RW2;
    input C1RW3;
    input C1TS;
    input C1TW;
    output C2RED;
    input C2RNE;
    input C2RS;
    input C2RW1;
    input C2RW2;
    input C2RW3;
    input C2TS;
    input C2TW;
    input CAD1;
    input CAD2;
    input CAD3;
    input CAD4;
    input CAD5;
    input CAD6;
    output CAL;
    input CAN1;
    input CAN2;
    input CAN3;
    input CAN4;
    input CAP1;
    input CAP2;
    input CAP3;
    input CAP4;
    input CAT1;
    input CAT2;
    input CAT3;
    input CAT4;
    input CCK;
    output CKO1;
    output CKO2;
    input CTCK;
    input DC;
    input DCK;
    input DIG;
    input DIS;
    input DOG;
    input DOS;
    input DPAG;
    input DPAS;
    input DQSG;
    input DQSS;
    input DRA1;
    input DRA2;
    input DRA3;
    input DRA4;
    input DRA5;
    input DRA6;
    input DRI1;
    input DRI2;
    input DRI3;
    input DRI4;
    input DRI5;
    input DRI6;
    input DRL;
    output DRO1;
    output DRO2;
    output DRO3;
    output DRO4;
    output DRO5;
    output DRO6;
    input DS1;
    input DS2;
    input FA1;
    input FA2;
    input FA3;
    input FA4;
    input FA5;
    input FA6;
    output FLD;
    output FLG;
    input FZ;
    input P10CI1;
    input P10CL;
    output P10CO;
    input P10CR;
    input P10CTI;
    output P10CTO;
    input P10EI1;
    input P10EI2;
    input P10EI3;
    input P10EI4;
    input P10EI5;
    input P10EL;
    output P10EO;
    input P10ER;
    input P10RI;
    input P10RL;
    output P10RO1;
    output P10RO2;
    output P10RO3;
    output P10RO4;
    output P10RO5;
    input P10RR;
    input P11CI1;
    input P11CL;
    output P11CO;
    input P11CR;
    input P11CTI;
    output P11CTO;
    input P11EI1;
    input P11EI2;
    input P11EI3;
    input P11EI4;
    input P11EI5;
    input P11EL;
    output P11EO;
    input P11ER;
    input P11RI;
    input P11RL;
    output P11RO1;
    output P11RO2;
    output P11RO3;
    output P11RO4;
    output P11RO5;
    input P11RR;
    input P12CI1;
    input P12CL;
    output P12CO;
    input P12CR;
    input P12CTI;
    output P12CTO;
    input P12EI1;
    input P12EI2;
    input P12EI3;
    input P12EI4;
    input P12EI5;
    input P12EL;
    output P12EO;
    input P12ER;
    input P12RI;
    input P12RL;
    output P12RO1;
    output P12RO2;
    output P12RO3;
    output P12RO4;
    output P12RO5;
    input P12RR;
    input P13CI1;
    input P13CL;
    output P13CO;
    input P13CR;
    input P13CTI;
    output P13CTO;
    input P13EI1;
    input P13EI2;
    input P13EI3;
    input P13EI4;
    input P13EI5;
    input P13EL;
    output P13EO;
    input P13ER;
    input P13RI;
    input P13RL;
    output P13RO1;
    output P13RO2;
    output P13RO3;
    output P13RO4;
    output P13RO5;
    input P13RR;
    input P14CI1;
    input P14CL;
    output P14CO;
    input P14CR;
    input P14CTI;
    output P14CTO;
    input P14EI1;
    input P14EI2;
    input P14EI3;
    input P14EI4;
    input P14EI5;
    input P14EL;
    output P14EO;
    input P14ER;
    input P14RI;
    input P14RL;
    output P14RO1;
    output P14RO2;
    output P14RO3;
    output P14RO4;
    output P14RO5;
    input P14RR;
    input P15CI1;
    input P15CL;
    output P15CO;
    input P15CR;
    input P15CTI;
    output P15CTO;
    input P15EI1;
    input P15EI2;
    input P15EI3;
    input P15EI4;
    input P15EI5;
    input P15EL;
    output P15EO;
    input P15ER;
    input P15RI;
    input P15RL;
    output P15RO1;
    output P15RO2;
    output P15RO3;
    output P15RO4;
    output P15RO5;
    input P15RR;
    input P16CI1;
    input P16CL;
    output P16CO;
    input P16CR;
    input P16CTI;
    output P16CTO;
    input P16EI1;
    input P16EI2;
    input P16EI3;
    input P16EI4;
    input P16EI5;
    input P16EL;
    output P16EO;
    input P16ER;
    input P16RI;
    input P16RL;
    output P16RO1;
    output P16RO2;
    output P16RO3;
    output P16RO4;
    output P16RO5;
    input P16RR;
    input P17CI1;
    input P17CL;
    output P17CO;
    input P17CR;
    input P17CTI;
    output P17CTO;
    input P17EI1;
    input P17EI2;
    input P17EI3;
    input P17EI4;
    input P17EI5;
    input P17EL;
    output P17EO;
    input P17ER;
    input P17RI;
    input P17RL;
    output P17RO1;
    output P17RO2;
    output P17RO3;
    output P17RO4;
    output P17RO5;
    input P17RR;
    input P18CI1;
    input P18CL;
    output P18CO;
    input P18CR;
    input P18CTI;
    output P18CTO;
    input P18EI1;
    input P18EI2;
    input P18EI3;
    input P18EI4;
    input P18EI5;
    input P18EL;
    output P18EO;
    input P18ER;
    input P18RI;
    input P18RL;
    output P18RO1;
    output P18RO2;
    output P18RO3;
    output P18RO4;
    output P18RO5;
    input P18RR;
    input P19CI1;
    input P19CL;
    output P19CO;
    input P19CR;
    input P19CTI;
    output P19CTO;
    input P19EI1;
    input P19EI2;
    input P19EI3;
    input P19EI4;
    input P19EI5;
    input P19EL;
    output P19EO;
    input P19ER;
    input P19RI;
    input P19RL;
    output P19RO1;
    output P19RO2;
    output P19RO3;
    output P19RO4;
    output P19RO5;
    input P19RR;
    input P1CI1;
    input P1CL;
    output P1CO;
    input P1CR;
    input P1CTI;
    output P1CTO;
    input P1EI1;
    input P1EI2;
    input P1EI3;
    input P1EI4;
    input P1EI5;
    input P1EL;
    output P1EO;
    input P1ER;
    input P1RI;
    input P1RL;
    output P1RO1;
    output P1RO2;
    output P1RO3;
    output P1RO4;
    output P1RO5;
    input P1RR;
    input P20CI1;
    input P20CL;
    output P20CO;
    input P20CR;
    input P20CTI;
    output P20CTO;
    input P20EI1;
    input P20EI2;
    input P20EI3;
    input P20EI4;
    input P20EI5;
    input P20EL;
    output P20EO;
    input P20ER;
    input P20RI;
    input P20RL;
    output P20RO1;
    output P20RO2;
    output P20RO3;
    output P20RO4;
    output P20RO5;
    input P20RR;
    input P21CI1;
    input P21CL;
    output P21CO;
    input P21CR;
    input P21CTI;
    output P21CTO;
    input P21EI1;
    input P21EI2;
    input P21EI3;
    input P21EI4;
    input P21EI5;
    input P21EL;
    output P21EO;
    input P21ER;
    input P21RI;
    input P21RL;
    output P21RO1;
    output P21RO2;
    output P21RO3;
    output P21RO4;
    output P21RO5;
    input P21RR;
    input P22CI1;
    input P22CL;
    output P22CO;
    input P22CR;
    input P22CTI;
    output P22CTO;
    input P22EI1;
    input P22EI2;
    input P22EI3;
    input P22EI4;
    input P22EI5;
    input P22EL;
    output P22EO;
    input P22ER;
    input P22RI;
    input P22RL;
    output P22RO1;
    output P22RO2;
    output P22RO3;
    output P22RO4;
    output P22RO5;
    input P22RR;
    input P23CI1;
    input P23CL;
    output P23CO;
    input P23CR;
    input P23CTI;
    output P23CTO;
    input P23EI1;
    input P23EI2;
    input P23EI3;
    input P23EI4;
    input P23EI5;
    input P23EL;
    output P23EO;
    input P23ER;
    input P23RI;
    input P23RL;
    output P23RO1;
    output P23RO2;
    output P23RO3;
    output P23RO4;
    output P23RO5;
    input P23RR;
    input P24CI1;
    input P24CL;
    output P24CO;
    input P24CR;
    input P24CTI;
    output P24CTO;
    input P24EI1;
    input P24EI2;
    input P24EI3;
    input P24EI4;
    input P24EI5;
    input P24EL;
    output P24EO;
    input P24ER;
    input P24RI;
    input P24RL;
    output P24RO1;
    output P24RO2;
    output P24RO3;
    output P24RO4;
    output P24RO5;
    input P24RR;
    input P25CI1;
    input P25CL;
    output P25CO;
    input P25CR;
    input P25CTI;
    output P25CTO;
    input P25EI1;
    input P25EI2;
    input P25EI3;
    input P25EI4;
    input P25EI5;
    input P25EL;
    output P25EO;
    input P25ER;
    input P25RI;
    input P25RL;
    output P25RO1;
    output P25RO2;
    output P25RO3;
    output P25RO4;
    output P25RO5;
    input P25RR;
    input P26CI1;
    input P26CL;
    output P26CO;
    input P26CR;
    input P26CTI;
    output P26CTO;
    input P26EI1;
    input P26EI2;
    input P26EI3;
    input P26EI4;
    input P26EI5;
    input P26EL;
    output P26EO;
    input P26ER;
    input P26RI;
    input P26RL;
    output P26RO1;
    output P26RO2;
    output P26RO3;
    output P26RO4;
    output P26RO5;
    input P26RR;
    input P27CI1;
    input P27CL;
    output P27CO;
    input P27CR;
    input P27CTI;
    output P27CTO;
    input P27EI1;
    input P27EI2;
    input P27EI3;
    input P27EI4;
    input P27EI5;
    input P27EL;
    output P27EO;
    input P27ER;
    input P27RI;
    input P27RL;
    output P27RO1;
    output P27RO2;
    output P27RO3;
    output P27RO4;
    output P27RO5;
    input P27RR;
    input P28CI1;
    input P28CL;
    output P28CO;
    input P28CR;
    input P28CTI;
    output P28CTO;
    input P28EI1;
    input P28EI2;
    input P28EI3;
    input P28EI4;
    input P28EI5;
    input P28EL;
    output P28EO;
    input P28ER;
    input P28RI;
    input P28RL;
    output P28RO1;
    output P28RO2;
    output P28RO3;
    output P28RO4;
    output P28RO5;
    input P28RR;
    input P29CI1;
    input P29CI2;
    input P29CI3;
    input P29CI4;
    input P29CI5;
    input P29CL;
    output P29CO;
    input P29CR;
    input P29CTI;
    output P29CTO;
    input P29EI1;
    input P29EI2;
    input P29EI3;
    input P29EI4;
    input P29EI5;
    input P29EL;
    output P29EO;
    input P29ER;
    input P29RI;
    input P29RL;
    output P29RO1;
    output P29RO2;
    output P29RO3;
    output P29RO4;
    output P29RO5;
    input P29RR;
    input P2CI1;
    input P2CL;
    output P2CO;
    input P2CR;
    input P2CTI;
    output P2CTO;
    input P2EI1;
    input P2EI2;
    input P2EI3;
    input P2EI4;
    input P2EI5;
    input P2EL;
    output P2EO;
    input P2ER;
    input P2RI;
    input P2RL;
    output P2RO1;
    output P2RO2;
    output P2RO3;
    output P2RO4;
    output P2RO5;
    input P2RR;
    input P30CI1;
    input P30CL;
    output P30CO;
    input P30CR;
    input P30CTI;
    output P30CTO;
    input P30EI1;
    input P30EI2;
    input P30EI3;
    input P30EI4;
    input P30EI5;
    input P30EL;
    output P30EO;
    input P30ER;
    input P30RI;
    input P30RL;
    output P30RO1;
    output P30RO2;
    output P30RO3;
    output P30RO4;
    output P30RO5;
    input P30RR;
    input P31CI1;
    input P31CL;
    output P31CO;
    input P31CR;
    input P31CTI;
    output P31CTO;
    input P31EI1;
    input P31EI2;
    input P31EI3;
    input P31EI4;
    input P31EI5;
    input P31EL;
    output P31EO;
    input P31ER;
    input P31RI;
    input P31RL;
    output P31RO1;
    output P31RO2;
    output P31RO3;
    output P31RO4;
    output P31RO5;
    input P31RR;
    input P32CI1;
    input P32CL;
    output P32CO;
    input P32CR;
    input P32CTI;
    output P32CTO;
    input P32EI1;
    input P32EI2;
    input P32EI3;
    input P32EI4;
    input P32EI5;
    input P32EL;
    output P32EO;
    input P32ER;
    input P32RI;
    input P32RL;
    output P32RO1;
    output P32RO2;
    output P32RO3;
    output P32RO4;
    output P32RO5;
    input P32RR;
    input P33CI1;
    input P33CL;
    output P33CO;
    input P33CR;
    input P33CTI;
    output P33CTO;
    input P33EI1;
    input P33EI2;
    input P33EI3;
    input P33EI4;
    input P33EI5;
    input P33EL;
    output P33EO;
    input P33ER;
    input P33RI;
    input P33RL;
    output P33RO1;
    output P33RO2;
    output P33RO3;
    output P33RO4;
    output P33RO5;
    input P33RR;
    input P34CI1;
    input P34CL;
    output P34CO;
    input P34CR;
    input P34CTI;
    output P34CTO;
    input P34EI1;
    input P34EI2;
    input P34EI3;
    input P34EI4;
    input P34EI5;
    input P34EL;
    output P34EO;
    input P34ER;
    input P34RI;
    input P34RL;
    output P34RO1;
    output P34RO2;
    output P34RO3;
    output P34RO4;
    output P34RO5;
    input P34RR;
    input P3CI1;
    input P3CL;
    output P3CO;
    input P3CR;
    input P3CTI;
    output P3CTO;
    input P3EI1;
    input P3EI2;
    input P3EI3;
    input P3EI4;
    input P3EI5;
    input P3EL;
    output P3EO;
    input P3ER;
    input P3RI;
    input P3RL;
    output P3RO1;
    output P3RO2;
    output P3RO3;
    output P3RO4;
    output P3RO5;
    input P3RR;
    input P4CI1;
    input P4CL;
    output P4CO;
    input P4CR;
    input P4CTI;
    output P4CTO;
    input P4EI1;
    input P4EI2;
    input P4EI3;
    input P4EI4;
    input P4EI5;
    input P4EL;
    output P4EO;
    input P4ER;
    input P4RI;
    input P4RL;
    output P4RO1;
    output P4RO2;
    output P4RO3;
    output P4RO4;
    output P4RO5;
    input P4RR;
    input P5CI1;
    input P5CI2;
    input P5CI3;
    input P5CI4;
    input P5CI5;
    input P5CL;
    output P5CO;
    input P5CR;
    input P5CTI;
    output P5CTO;
    input P5EI1;
    input P5EI2;
    input P5EI3;
    input P5EI4;
    input P5EI5;
    input P5EL;
    output P5EO;
    input P5ER;
    input P5RI;
    input P5RL;
    output P5RO1;
    output P5RO2;
    output P5RO3;
    output P5RO4;
    output P5RO5;
    input P5RR;
    input P6CI1;
    input P6CL;
    output P6CO;
    input P6CR;
    input P6CTI;
    output P6CTO;
    input P6EI1;
    input P6EI2;
    input P6EI3;
    input P6EI4;
    input P6EI5;
    input P6EL;
    output P6EO;
    input P6ER;
    input P6RI;
    input P6RL;
    output P6RO1;
    output P6RO2;
    output P6RO3;
    output P6RO4;
    output P6RO5;
    input P6RR;
    input P7CI1;
    input P7CL;
    output P7CO;
    input P7CR;
    input P7CTI;
    output P7CTO;
    input P7EI1;
    input P7EI2;
    input P7EI3;
    input P7EI4;
    input P7EI5;
    input P7EL;
    output P7EO;
    input P7ER;
    input P7RI;
    input P7RL;
    output P7RO1;
    output P7RO2;
    output P7RO3;
    output P7RO4;
    output P7RO5;
    input P7RR;
    input P8CI1;
    input P8CL;
    output P8CO;
    input P8CR;
    input P8CTI;
    output P8CTO;
    input P8EI1;
    input P8EI2;
    input P8EI3;
    input P8EI4;
    input P8EI5;
    input P8EL;
    output P8EO;
    input P8ER;
    input P8RI;
    input P8RL;
    output P8RO1;
    output P8RO2;
    output P8RO3;
    output P8RO4;
    output P8RO5;
    input P8RR;
    input P9CI1;
    input P9CL;
    output P9CO;
    input P9CR;
    input P9CTI;
    output P9CTO;
    input P9EI1;
    input P9EI2;
    input P9EI3;
    input P9EI4;
    input P9EI5;
    input P9EL;
    output P9EO;
    input P9ER;
    input P9RI;
    input P9RL;
    output P9RO1;
    output P9RO2;
    output P9RO3;
    output P9RO4;
    output P9RO5;
    input P9RR;
    input RRCK1;
    input RRCK2;
    input RTCK1;
    input RTCK2;
    input WRCK1;
    input WRCK2;
    input WTCK1;
    input WTCK2;
    parameter div_rx1 = 4'b0000;
    parameter div_rx2 = 4'b0000;
    parameter div_tx1 = 4'b0000;
    parameter div_tx2 = 4'b0000;
    parameter mode_io_cal = 1'b0;
    parameter mode_side1 = 0;
    parameter mode_side2 = 0;
    parameter pads_dict = "";
    parameter pads_path = "";
    parameter sel_clk_out1 = 1'b0;
    parameter sel_clk_out2 = 1'b0;
    parameter sel_clkr_rx1 = 1'b0;
    parameter sel_clkr_rx2 = 1'b0;
    parameter sel_clkw_rx1 = 2'b00;
    parameter sel_clkw_rx2 = 2'b00;
endmodule

(* blackbox *)
module NX_PMA_L(CLK_USER_I, CLK_REF_I, PRE_SG_I, PRE_EN_I, PRE_IS_I1, PRE_IS_I2, PRE_IS_I3, PRE_IS_I4, MAIN_SG_I, MAIN_EN_I1, MAIN_EN_I2, MAIN_EN_I3, MAIN_EN_I4, MAIN_EN_I5, MAIN_EN_I6, MARG_S_I1, MARG_S_I2, MARG_S_I3, MARG_S_I4, MARG_IS_I1, MARG_IS_I2
, MARG_IS_I3, MARG_IS_I4, MARG_SV_I1, MARG_SV_I2, MARG_SV_I3, MARG_SV_I4, MARG_SV_I5, MARG_ISV_I1, MARG_ISV_I2, MARG_ISV_I3, MARG_ISV_I4, MARG_ISV_I5, POST_EN_I1, POST_EN_I2, POST_EN_I3, POST_EN_I4, POST_EN_I5, POST_SG_I, POST_IS_I1, POST_IS_I2, POST_IS_I3
, POST_IS_I4, POST_ISV_I1, POST_ISV_I2, POST_ISV_I3, POST_ISV_I4, TX_SEL_I1, TX_SEL_I2, TX_SEL_I3, TX_SEL_I4, TX_SEL_I5, TX_SEL_I6, CT_CAP_I1, CT_CAP_I2, CT_CAP_I3, CT_CAP_I4, CT_RESP_I1, CT_RESP_I2, CT_RESP_I3, CT_RESP_I4, CT_RESN_I1, CT_RESN_I2
, CT_RESN_I3, CT_RESN_I4, M_EYE_I, RX_SEL_I1, RX_SEL_I2, RX_SEL_I3, RX_SEL_I4, RX_SEL_I5, RX_SEL_I6, PLL_RN_I, RST_N_I, CAL_1P_I1, CAL_1P_I2, CAL_1P_I3, CAL_1P_I4, CAL_1P_I5, CAL_1P_I6, CAL_1P_I7, CAL_1P_I8, CAL_2N_I1, CAL_2N_I2
, CAL_2N_I3, CAL_2N_I4, CAL_2N_I5, CAL_2N_I6, CAL_2N_I7, CAL_2N_I8, CAL_3N_I1, CAL_3N_I2, CAL_3N_I3, CAL_3N_I4, CAL_3N_I5, CAL_3N_I6, CAL_3N_I7, CAL_3N_I8, CAL_4P_I1, CAL_4P_I2, CAL_4P_I3, CAL_4P_I4, CAL_4P_I5, CAL_4P_I6, CAL_4P_I7
, CAL_4P_I8, CAL_SEL_I1, CAL_SEL_I2, CAL_SEL_I3, CAL_SEL_I4, CAL_E_I, LOCK_E_I, OVS_E_I, TST_I1, TST_I2, TST_I3, TST_I4, TST_I5, TST_I6, TST_I7, TST_I8, CLK_O, LOCK_O, CAL_O, TST_O1, TST_O2
, TST_O3, TST_O4, TST_O5, TST_O6, TST_O7, TST_O8, CLK_EXT_I, LINK_TX1, LINK_TX2, LINK_TX3, LINK_TX4, LINK_TX5, LINK_RX0, LINK_RX1, LINK_RX2, LINK_RX3, LINK_RX4, LINK_RX5, LINK_TX0);
    input CAL_1P_I1;
    input CAL_1P_I2;
    input CAL_1P_I3;
    input CAL_1P_I4;
    input CAL_1P_I5;
    input CAL_1P_I6;
    input CAL_1P_I7;
    input CAL_1P_I8;
    input CAL_2N_I1;
    input CAL_2N_I2;
    input CAL_2N_I3;
    input CAL_2N_I4;
    input CAL_2N_I5;
    input CAL_2N_I6;
    input CAL_2N_I7;
    input CAL_2N_I8;
    input CAL_3N_I1;
    input CAL_3N_I2;
    input CAL_3N_I3;
    input CAL_3N_I4;
    input CAL_3N_I5;
    input CAL_3N_I6;
    input CAL_3N_I7;
    input CAL_3N_I8;
    input CAL_4P_I1;
    input CAL_4P_I2;
    input CAL_4P_I3;
    input CAL_4P_I4;
    input CAL_4P_I5;
    input CAL_4P_I6;
    input CAL_4P_I7;
    input CAL_4P_I8;
    input CAL_E_I;
    output CAL_O;
    input CAL_SEL_I1;
    input CAL_SEL_I2;
    input CAL_SEL_I3;
    input CAL_SEL_I4;
    input CLK_EXT_I;
    output CLK_O;
    input CLK_REF_I;
    input CLK_USER_I;
    input CT_CAP_I1;
    input CT_CAP_I2;
    input CT_CAP_I3;
    input CT_CAP_I4;
    input CT_RESN_I1;
    input CT_RESN_I2;
    input CT_RESN_I3;
    input CT_RESN_I4;
    input CT_RESP_I1;
    input CT_RESP_I2;
    input CT_RESP_I3;
    input CT_RESP_I4;
    inout [9:0] LINK_RX0;
    inout [9:0] LINK_RX1;
    inout [9:0] LINK_RX2;
    inout [9:0] LINK_RX3;
    inout [9:0] LINK_RX4;
    inout [9:0] LINK_RX5;
    inout [19:0] LINK_TX0;
    inout [19:0] LINK_TX1;
    inout [19:0] LINK_TX2;
    inout [19:0] LINK_TX3;
    inout [19:0] LINK_TX4;
    inout [19:0] LINK_TX5;
    input LOCK_E_I;
    output LOCK_O;
    input MAIN_EN_I1;
    input MAIN_EN_I2;
    input MAIN_EN_I3;
    input MAIN_EN_I4;
    input MAIN_EN_I5;
    input MAIN_EN_I6;
    input MAIN_SG_I;
    input MARG_ISV_I1;
    input MARG_ISV_I2;
    input MARG_ISV_I3;
    input MARG_ISV_I4;
    input MARG_ISV_I5;
    input MARG_IS_I1;
    input MARG_IS_I2;
    input MARG_IS_I3;
    input MARG_IS_I4;
    input MARG_SV_I1;
    input MARG_SV_I2;
    input MARG_SV_I3;
    input MARG_SV_I4;
    input MARG_SV_I5;
    input MARG_S_I1;
    input MARG_S_I2;
    input MARG_S_I3;
    input MARG_S_I4;
    input M_EYE_I;
    input OVS_E_I;
    input PLL_RN_I;
    input POST_EN_I1;
    input POST_EN_I2;
    input POST_EN_I3;
    input POST_EN_I4;
    input POST_EN_I5;
    input POST_ISV_I1;
    input POST_ISV_I2;
    input POST_ISV_I3;
    input POST_ISV_I4;
    input POST_IS_I1;
    input POST_IS_I2;
    input POST_IS_I3;
    input POST_IS_I4;
    input POST_SG_I;
    input PRE_EN_I;
    input PRE_IS_I1;
    input PRE_IS_I2;
    input PRE_IS_I3;
    input PRE_IS_I4;
    input PRE_SG_I;
    input RST_N_I;
    input RX_SEL_I1;
    input RX_SEL_I2;
    input RX_SEL_I3;
    input RX_SEL_I4;
    input RX_SEL_I5;
    input RX_SEL_I6;
    input TST_I1;
    input TST_I2;
    input TST_I3;
    input TST_I4;
    input TST_I5;
    input TST_I6;
    input TST_I7;
    input TST_I8;
    output TST_O1;
    output TST_O2;
    output TST_O3;
    output TST_O4;
    output TST_O5;
    output TST_O6;
    output TST_O7;
    output TST_O8;
    input TX_SEL_I1;
    input TX_SEL_I2;
    input TX_SEL_I3;
    input TX_SEL_I4;
    input TX_SEL_I5;
    input TX_SEL_I6;
    parameter location = "";
    parameter main_clk_to_fabric_div_en = 1'b0;
    parameter main_clk_to_fabric_div_mode = 1'b0;
    parameter main_clk_to_fabric_sel = 1'b0;
    parameter main_test = 8'b00000000;
    parameter main_use_only_usr_clock = 1'b0;
    parameter main_use_pcs_clk_2 = 1'b0;
    parameter pcs_ovs_mode = 1'b0;
    parameter pcs_pll_lock_count = 3'b000;
    parameter pcs_word_len = 2'b00;
    parameter pll_pma_cpump_n = 3'b000;
    parameter pll_pma_divf = 2'b00;
    parameter pll_pma_divf_en_n = 1'b0;
    parameter pll_pma_divm = 2'b00;
    parameter pll_pma_divm_en_n = 1'b0;
    parameter pll_pma_divn = 1'b0;
    parameter pll_pma_divn_en_n = 1'b0;
    parameter pll_pma_int_data_len = 1'b0;
    parameter pll_pma_lvds_mux = 1'b0;
    parameter pll_pma_mux_ckref = 1'b0;
    parameter rx_pma_half_step = 1'b0;
endmodule

(* blackbox *)
module NX_RFB_L(RCK, WCK, I1, I2, I3, I4, I5, I6, I7, I8, I9, I10, I11, I12, I13, I14, I15, I16, COR, ERR, O1
, O2, O3, O4, O5, O6, O7, O8, O9, O10, O11, O12, O13, O14, O15, O16, RA1, RA2, RA3, RA4, RA5, RA6
, RE, WA1, WA2, WA3, WA4, WA5, WA6, WE);
    output COR;
    output ERR;
    input I1;
    input I10;
    input I11;
    input I12;
    input I13;
    input I14;
    input I15;
    input I16;
    input I2;
    input I3;
    input I4;
    input I5;
    input I6;
    input I7;
    input I8;
    input I9;
    output O1;
    output O10;
    output O11;
    output O12;
    output O13;
    output O14;
    output O15;
    output O16;
    output O2;
    output O3;
    output O4;
    output O5;
    output O6;
    output O7;
    output O8;
    output O9;
    input RA1;
    input RA2;
    input RA3;
    input RA4;
    input RA5;
    input RA6;
    input RCK;
    input RE;
    input WA1;
    input WA2;
    input WA3;
    input WA4;
    input WA5;
    input WA6;
    input WCK;
    input WE;
    parameter mem_ctxt = "";
    parameter mode = 0;
    parameter rck_edge = 1'b0;
    parameter wck_edge = 1'b0;
endmodule

(* blackbox *)
module NX_IOM_CONTROL_L(RTCK1, RRCK1, WTCK1, WRCK1, RTCK2, RRCK2, WTCK2, WRCK2, CTCK, C1TW, C1TS, C1RW1, C1RW2, C1RW3, C1RNE, C1RS, C2TW, C2TS, C2RW1, C2RW2, C2RW3
, C2RNE, C2RS, FA1, FA2, FA3, FA4, FA5, FA6, FZ, DC, CCK, DCK, DRI1, DRI2, DRI3, DRI4, DRI5, DRI6, DRA1, DRA2, DRA3
, DRA4, DRA5, DRA6, DRL, DOS, DOG, DIS, DIG, DPAS, DPAG, DQSS, DQSG, DS1, DS2, CAD1, CAD2, CAD3, CAD4, CAD5, CAD6, CAP1
, CAP2, CAP3, CAP4, CAN1, CAN2, CAN3, CAN4, CAT1, CAT2, CAT3, CAT4, CKO1, CKO2, FLD, FLG, C1RED, C2RED, DRO1, DRO2, DRO3, DRO4
, DRO5, DRO6, CAL, LINK2, LINK3, LINK4, LINK5, LINK6, LINK7, LINK8, LINK9, LINK10, LINK11, LINK12, LINK13, LINK14, LINK15, LINK16, LINK17, LINK18, LINK19
, LINK20, LINK21, LINK22, LINK23, LINK24, LINK25, LINK26, LINK27, LINK28, LINK29, LINK30, LINK31, LINK32, LINK33, LINK34, LINK1);
    output C1RED;
    input C1RNE;
    input C1RS;
    input C1RW1;
    input C1RW2;
    input C1RW3;
    input C1TS;
    input C1TW;
    output C2RED;
    input C2RNE;
    input C2RS;
    input C2RW1;
    input C2RW2;
    input C2RW3;
    input C2TS;
    input C2TW;
    input CAD1;
    input CAD2;
    input CAD3;
    input CAD4;
    input CAD5;
    input CAD6;
    output CAL;
    input CAN1;
    input CAN2;
    input CAN3;
    input CAN4;
    input CAP1;
    input CAP2;
    input CAP3;
    input CAP4;
    input CAT1;
    input CAT2;
    input CAT3;
    input CAT4;
    input CCK;
    output CKO1;
    output CKO2;
    input CTCK;
    input DC;
    input DCK;
    input DIG;
    input DIS;
    input DOG;
    input DOS;
    input DPAG;
    input DPAS;
    input DQSG;
    input DQSS;
    input DRA1;
    input DRA2;
    input DRA3;
    input DRA4;
    input DRA5;
    input DRA6;
    input DRI1;
    input DRI2;
    input DRI3;
    input DRI4;
    input DRI5;
    input DRI6;
    input DRL;
    output DRO1;
    output DRO2;
    output DRO3;
    output DRO4;
    output DRO5;
    output DRO6;
    input DS1;
    input DS2;
    input FA1;
    input FA2;
    input FA3;
    input FA4;
    input FA5;
    input FA6;
    output FLD;
    output FLG;
    input FZ;
    inout [41:0] LINK1;
    inout [41:0] LINK10;
    inout [41:0] LINK11;
    inout [41:0] LINK12;
    inout [41:0] LINK13;
    inout [41:0] LINK14;
    inout [41:0] LINK15;
    inout [41:0] LINK16;
    inout [41:0] LINK17;
    inout [41:0] LINK18;
    inout [41:0] LINK19;
    inout [41:0] LINK2;
    inout [41:0] LINK20;
    inout [41:0] LINK21;
    inout [41:0] LINK22;
    inout [41:0] LINK23;
    inout [41:0] LINK24;
    inout [41:0] LINK25;
    inout [41:0] LINK26;
    inout [41:0] LINK27;
    inout [41:0] LINK28;
    inout [41:0] LINK29;
    inout [41:0] LINK3;
    inout [41:0] LINK30;
    inout [41:0] LINK31;
    inout [41:0] LINK32;
    inout [41:0] LINK33;
    inout [41:0] LINK34;
    inout [41:0] LINK4;
    inout [41:0] LINK5;
    inout [41:0] LINK6;
    inout [41:0] LINK7;
    inout [41:0] LINK8;
    inout [41:0] LINK9;
    input RRCK1;
    input RRCK2;
    input RTCK1;
    input RTCK2;
    input WRCK1;
    input WRCK2;
    input WTCK1;
    input WTCK2;
    parameter div_rx1 = 4'b0000;
    parameter div_rx2 = 4'b0000;
    parameter div_tx1 = 4'b0000;
    parameter div_tx2 = 4'b0000;
    parameter inv_di_fclk1 = 1'b0;
    parameter inv_di_fclk2 = 1'b0;
    parameter latency1 = 1'b0;
    parameter latency2 = 1'b0;
    parameter location = "";
    parameter mode_cpath = "";
    parameter mode_epath = "";
    parameter mode_io_cal = 1'b0;
    parameter mode_rpath = "";
    parameter mode_side1 = 0;
    parameter mode_side2 = 0;
    parameter mode_tpath = "";
    parameter sel_clk_out1 = 1'b0;
    parameter sel_clk_out2 = 1'b0;
    parameter sel_clkr_rx1 = 1'b0;
    parameter sel_clkr_rx2 = 1'b0;
    parameter sel_clkw_rx1 = 2'b00;
    parameter sel_clkw_rx2 = 2'b00;
endmodule

