X(A)
X(abc9_box)
X(abc9_box_id)
X(abc9_box_seq)
X(abc9_bypass)
X(abc9_carry)
X(abc9_flop)
X(abc9_keep)
X(abc9_lut)
X(abc9_mergeability)
X(abc9_scc_id)
X(abcgroup)
X(ABITS)
X(AD)
X(ADDR)
X(allconst)
X(allseq)
X(ALOAD)
X(ALOAD_POLARITY)
X(always_comb)
X(always_ff)
X(always_latch)
X(anyconst)
X(anyseq)
X(ARGS)
X(ARGS_WIDTH)
X(ARST)
X(ARST_POLARITY)
X(ARST_VALUE)
X(A_SIGNED)
X(A_WIDTH)
X(B)
X(BI)
X(BITS_USED)
X(blackbox)
X(B_SIGNED)
X(bugpoint_keep)
X(B_WIDTH)
X(BYTE)
X(C)
X(cells_not_processed)
X(CE_OVER_SRST)
X(CFG_ABITS)
X(CFG_DBITS)
X(CFG_INIT)
X(chain)
X(CI)
X(CLK)
X(clkbuf_driver)
X(clkbuf_inhibit)
X(clkbuf_inv)
X(clkbuf_sink)
X(CLK_ENABLE)
X(CLK_POLARITY)
X(CLR)
X(CLR_POLARITY)
X(CO)
X(COLLISION_X_MASK)
X(CONFIG)
X(CONFIG_WIDTH)
X(CTRL_IN)
X(CTRL_IN_WIDTH)
X(CTRL_OUT)
X(CTRL_OUT_WIDTH)
X(D)
X(DAT)
X(DATA)
X(DAT_DST_PEN)
X(DAT_DST_POL)
X(defaultvalue)
X(DELAY)
X(DEPTH)
X(DST)
X(DST_EN)
X(DST_PEN)
X(DST_POL)
X(DST_WIDTH)
X(dynports)
X(E)
X(EDGE_EN)
X(EDGE_POL)
X(EN)
X(EN_DST)
X(EN_POLARITY)
X(EN_SRC)
X(enum_base_type)
X(enum_type)
X(equiv_merged)
X(equiv_region)
X(extract_order)
X(F)
X(FLAVOR)
X(FORMAT)
X(force_downto)
X(force_upto)
X(fsm_encoding)
X(fsm_export)
X(FULL)
X(full_case)
X(G)
X(gclk)
X(gentb_clock)
X(gentb_constant)
X(gentb_skip)
X(H)
X(hdlname)
X(hierconn)
X(I)
X(INIT)
X(INIT_VALUE)
X(init)
X(initial_top)
X(interface_modport)
X(interfaces_replaced_in_module)
X(interface_type)
X(invertible_pin)
X(iopad_external_pin)
X(is_interface)
X(J)
X(K)
X(keep)
X(keep_hierarchy)
X(L)
X(lib_whitebox)
X(localparam)
X(logic_block)
X(lram)
X(LUT)
X(lut_keep)
X(M)
X(maximize)
X(mem2reg)
X(MEMID)
X(minimize)
X(module_not_derived)
X(N)
X(NAME)
X(noblackbox)
X(nolatches)
X(nomem2init)
X(nomem2reg)
X(nomeminit)
X(nosync)
X(nowrshmsk)
X(no_ram)
X(no_rw_check)
X(O)
X(OFFSET)
X(onehot)
X(P)
X(parallel_case)
X(parameter)
X(PORTID)
X(PRIORITY)
X(PRIORITY_MASK)
X(promoted_if)
X(Q)
X(R)
X(ram_block)
X(ram_style)
X(ramstyle)
X(RD_ADDR)
X(RD_ARST)
X(RD_ARST_VALUE)
X(RD_CE_OVER_SRST)
X(RD_CLK)
X(RD_CLK_ENABLE)
X(RD_CLK_POLARITY)
X(RD_COLLISION_X_MASK)
X(RD_DATA)
X(RD_EN)
X(RD_INIT_VALUE)
X(RD_PORTS)
X(RD_SRST)
X(RD_SRST_VALUE)
X(RD_TRANSPARENCY_MASK)
X(RD_TRANSPARENT)
X(RD_WIDE_CONTINUATION)
X(reg)
X(replaced_by_gclk)
X(reprocess_after)
X(rom_block)
X(rom_style)
X(romstyle)
X(S)
X(SET)
X(SET_POLARITY)
X(single_bit_vector)
X(SIZE)
X(SRC)
X(src)
X(SRC_DST_PEN)
X(SRC_DST_POL)
X(SRC_EN)
X(SRC_PEN)
X(SRC_POL)
X(SRC_WIDTH)
X(SRST)
X(SRST_POLARITY)
X(SRST_VALUE)
X(sta_arrival)
X(STATE_BITS)
X(STATE_NUM)
X(STATE_NUM_LOG2)
X(STATE_RST)
X(STATE_TABLE)
X(smtlib2_module)
X(smtlib2_comb_expr)
X(submod)
X(syn_ramstyle)
X(syn_romstyle)
X(S_WIDTH)
X(T)
X(TABLE)
X(TAG)
X(techmap_autopurge)
X(_TECHMAP_BITS_CONNMAP_)
X(_TECHMAP_CELLNAME_)
X(_TECHMAP_CELLTYPE_)
X(techmap_celltype)
X(_TECHMAP_FAIL_)
X(techmap_maccmap)
X(_TECHMAP_REPLACE_)
X(techmap_simplemap)
X(_techmap_special_)
X(techmap_wrap)
X(_TECHMAP_PLACEHOLDER_)
X(techmap_chtype)
X(T_FALL_MAX)
X(T_FALL_MIN)
X(T_FALL_TYP)
X(T_LIMIT)
X(T_LIMIT2)
X(T_LIMIT2_MAX)
X(T_LIMIT2_MIN)
X(T_LIMIT2_TYP)
X(T_LIMIT_MAX)
X(T_LIMIT_MIN)
X(T_LIMIT_TYP)
X(to_delete)
X(top)
X(TRANS_NUM)
X(TRANSPARENCY_MASK)
X(TRANSPARENT)
X(TRANS_TABLE)
X(TRG)
X(TRG_ENABLE)
X(TRG_POLARITY)
X(TRG_WIDTH)
X(T_RISE_MAX)
X(T_RISE_MIN)
X(T_RISE_TYP)
X(TYPE)
X(U)
X(unique)
X(unused_bits)
X(V)
X(via_celltype)
X(wand)
X(whitebox)
X(WIDTH)
X(wildcard_port_conns)
X(wiretype)
X(wor)
X(WORDS)
X(WR_ADDR)
X(WR_CLK)
X(WR_CLK_ENABLE)
X(WR_CLK_POLARITY)
X(WR_DATA)
X(WR_EN)
X(WR_PORTS)
X(WR_PRIORITY_MASK)
X(WR_WIDE_CONTINUATION)
X(X)
X(xprop_decoder)
X(Y)
X(Y_WIDTH)
X(area)
X(capacitance)
X(NPRODUCTS)
X(NADDENDS)
X(PRODUCT_NEGATED)
X(ADDEND_NEGATED)
X(A_WIDTHS)
X(B_WIDTHS)
X(C_WIDTHS)
X(C_SIGNED)
