// Created by cells_xtra.py


module LUT5 (...);
parameter INIT = 32'h00000000;
input I0, I1, I2, I3, I4;
output F;
endmodule


module LUT6 (...);
parameter INIT = 64'h0000_0000_0000_0000;
input I0, I1, I2, I3, I4, I5;
output F;
endmodule


module LUT7 (...);
parameter INIT = 128'h0000_0000_0000_0000_0000_0000_0000_0000;
input I0, I1, I2, I3, I4, I5, I6;
output F;
endmodule


module LUT8 (...);
parameter INIT = 256'h0000_0000_0000_0000_0000_0000_0000_0000_0000_0000_0000_0000_0000_0000_0000_0000;
input I0, I1, I2, I3, I4, I5, I6, I7;
output F;
endmodule


module ROM16 (...);
parameter INIT_0 = 16'h0000;
input [3:0] AD;
output DO;
endmodule


module INV (...);
input  I;
output O;
endmodule


module TLVDS_IBUF (...);
output O;
input  I, IB;
endmodule

module TLVDS_TBUF (...);
output O, OB;
input  I, OEN;
endmodule

module TLVDS_IOBUF (...);
output   O;
inout IO, IOB;
input I, OEN;
endmodule

module ELVDS_TBUF (...);
output O, OB;
input  I, OEN;
endmodule

module ELVDS_IOBUF (...);
output   O;
inout IO, IOB;
input I, OEN;
endmodule

module MIPI_IBUF (...);
output OH, OL, OB;
inout IO, IOB;
input  I, IB;
input OEN, OENB;
input HSEN, HSREN;
endmodule

module MIPI_OBUF_A (...);
output O, OB;
input  I, IB, IL, MODESEL;
endmodule

module IBUF_R (...);
input  I;
input RTEN;
output O;
endmodule

module IOBUF_R (...);
input I,OEN;
input RTEN;
output O;
inout IO;
endmodule

module ELVDS_IOBUF_R (...);
output  O;
inout IO, IOB;
input I, OEN;
input RTEN;
endmodule

module I3C_IOBUF (...);
output O;
inout IO;
input  I, MODESEL;
endmodule

module TLVDS_IBUF_ADC (...);
input I, IB;
input ADCEN;
endmodule

module SDPB (...);
parameter READ_MODE = 1'b0; 
parameter BIT_WIDTH_0 = 32; 
parameter BIT_WIDTH_1 = 32; 
parameter BLK_SEL_0 = 3'b000;
parameter BLK_SEL_1 = 3'b000;
parameter RESET_MODE = "SYNC"; 
parameter INIT_RAM_00 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_01 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_02 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_03 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_04 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_05 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_06 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_07 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_08 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_09 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_10 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_11 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_12 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_13 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_14 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_15 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_16 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_17 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_18 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_19 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_20 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_21 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_22 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_23 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_24 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_25 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_26 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_27 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_28 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_29 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_30 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_31 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_32 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_33 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_34 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_35 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_36 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_37 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_38 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_39 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
input CLKA, CEA, CLKB, CEB;
input OCE; 
input RESET; 
input [13:0] ADA, ADB;
input [31:0] DI;
input [2:0] BLKSELA, BLKSELB;
output [31:0] DO;
endmodule


module SDPX9B (...);
parameter READ_MODE = 1'b0; 
parameter BIT_WIDTH_0 = 36; 
parameter BIT_WIDTH_1 = 36; 
parameter BLK_SEL_0 = 3'b000;
parameter BLK_SEL_1 = 3'b000;
parameter RESET_MODE = "SYNC"; 
parameter INIT_RAM_00 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000; 
parameter INIT_RAM_01 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_02 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_03 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_04 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_05 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_06 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_07 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_08 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_09 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0A = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0B = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0C = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0D = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0E = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0F = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_10 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_11 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_12 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_13 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_14 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_15 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_16 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_17 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_18 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_19 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1A = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1B = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1C = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1D = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1E = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1F = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_20 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_21 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_22 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_23 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_24 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_25 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_26 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_27 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_28 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_29 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2A = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2B = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2C = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2D = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2E = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2F = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_30 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_31 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_32 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_33 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_34 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_35 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_36 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_37 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_38 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_39 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3A = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3B = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3C = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3D = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3E = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3F = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
input CLKA, CEA, CLKB, CEB;
input OCE; 
input RESET; 
input [13:0] ADA, ADB;
input [2:0] BLKSELA, BLKSELB;
input [35:0] DI;
output [35:0] DO;
endmodule


module DPB (...);
parameter READ_MODE0 = 1'b0; 
parameter READ_MODE1 = 1'b0; 
parameter WRITE_MODE0 = 2'b00; 
parameter WRITE_MODE1 = 2'b00; 
parameter BIT_WIDTH_0 = 16; 
parameter BIT_WIDTH_1 = 16; 
parameter BLK_SEL_0 = 3'b000;
parameter BLK_SEL_1 = 3'b000;
parameter RESET_MODE = "SYNC"; 
parameter INIT_RAM_00 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_01 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_02 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_03 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_04 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_05 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_06 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_07 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_08 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_09 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_10 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_11 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_12 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_13 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_14 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_15 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_16 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_17 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_18 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_19 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_20 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_21 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_22 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_23 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_24 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_25 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_26 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_27 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_28 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_29 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_30 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_31 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_32 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_33 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_34 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_35 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_36 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_37 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_38 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_39 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
input CLKA, CEA, CLKB, CEB;
input OCEA, OCEB; 
input RESETA, RESETB; 
input WREA, WREB; 
input [13:0] ADA, ADB;
input [2:0] BLKSELA, BLKSELB;
input [15:0] DIA, DIB;
output [15:0] DOA, DOB;
endmodule


module DPX9B (...);
parameter READ_MODE0 = 1'b0; 
parameter READ_MODE1 = 1'b0; 
parameter WRITE_MODE0 = 2'b00; 
parameter WRITE_MODE1 = 2'b00; 
parameter BIT_WIDTH_0 = 18; 
parameter BIT_WIDTH_1 = 18; 
parameter BLK_SEL_0 = 3'b000;
parameter BLK_SEL_1 = 3'b000;
parameter RESET_MODE = "SYNC"; 
parameter INIT_RAM_00 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000; 
parameter INIT_RAM_01 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_02 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_03 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_04 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_05 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_06 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_07 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_08 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_09 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0A = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0B = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0C = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0D = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0E = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0F = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_10 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_11 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_12 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_13 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_14 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_15 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_16 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_17 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_18 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_19 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1A = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1B = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1C = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1D = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1E = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1F = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_20 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_21 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_22 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_23 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_24 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_25 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_26 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_27 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_28 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_29 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2A = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2B = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2C = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2D = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2E = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2F = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_30 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_31 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_32 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_33 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_34 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_35 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_36 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_37 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_38 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_39 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3A = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3B = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3C = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3D = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3E = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3F = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
input CLKA, CEA, CLKB, CEB;
input OCEA, OCEB; 
input RESETA, RESETB; 
input WREA, WREB; 
input [13:0] ADA, ADB;
input [17:0] DIA, DIB;
input [2:0] BLKSELA, BLKSELB;
output [17:0] DOA, DOB;
endmodule


module pROM (...);
parameter READ_MODE = 1'b0; 
parameter BIT_WIDTH = 32; 
parameter RESET_MODE = "SYNC"; 
parameter INIT_RAM_00 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_01 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_02 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_03 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_04 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_05 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_06 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_07 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_08 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_09 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_10 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_11 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_12 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_13 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_14 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_15 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_16 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_17 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_18 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_19 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_20 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_21 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_22 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_23 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_24 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_25 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_26 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_27 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_28 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_29 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_30 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_31 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_32 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_33 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_34 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_35 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_36 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_37 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_38 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_39 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
input CLK, CE;
input OCE; 
input RESET; 
input [13:0] AD;
output [31:0] DO;
endmodule


module pROMX9 (...);
parameter READ_MODE = 1'b0; 
parameter BIT_WIDTH = 36; 
parameter RESET_MODE = "SYNC"; 
parameter INIT_RAM_00 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000; 
parameter INIT_RAM_01 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_02 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_03 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_04 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_05 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_06 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_07 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_08 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_09 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0A = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0B = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0C = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0D = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0E = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0F = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_10 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_11 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_12 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_13 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_14 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_15 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_16 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_17 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_18 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_19 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1A = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1B = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1C = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1D = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1E = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1F = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_20 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_21 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_22 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_23 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_24 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_25 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_26 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_27 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_28 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_29 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2A = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2B = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2C = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2D = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2E = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2F = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_30 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_31 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_32 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_33 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_34 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_35 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_36 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_37 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_38 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_39 = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3A = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3B = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3C = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3D = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3E = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3F = 288'h000000000000000000000000000000000000000000000000000000000000000000000000;
input CLK, CE;
input OCE; 
input RESET; 
input [13:0] AD;
output [35:0] DO;
endmodule


module SDP36KE (...);
parameter ECC_WRITE_EN="FALSE"; 
parameter ECC_READ_EN="FALSE"; 
parameter READ_MODE = 1'b0; 
parameter BLK_SEL_A = 3'b000;
parameter BLK_SEL_B = 3'b000;
parameter RESET_MODE = "SYNC"; 
parameter INIT_FILE = "NONE"; 
parameter INIT_RAM_00 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_01 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_02 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_03 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_04 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_05 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_06 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_07 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_08 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_09 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_0F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_10 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_11 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_12 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_13 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_14 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_15 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_16 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_17 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_18 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_19 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_1F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_20 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_21 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_22 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_23 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_24 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_25 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_26 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_27 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_28 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_29 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_2F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_30 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_31 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_32 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_33 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_34 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_35 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_36 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_37 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_38 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_39 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_3F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_40 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_41 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_42 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_43 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_44 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_45 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_46 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_47 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_48 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_49 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_4A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_4B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_4C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_4D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_4E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_4F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_50 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_51 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_52 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_53 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_54 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_55 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_56 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_57 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_58 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_59 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_5A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_5B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_5C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_5D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_5E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_5F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_60 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_61 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_62 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_63 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_64 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_65 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_66 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_67 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_68 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_69 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_6A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_6B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_6C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_6D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_6E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_6F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_70 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_71 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_72 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_73 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_74 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_75 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_76 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_77 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_78 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_79 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_7A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_7B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_7C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_7D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_7E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INIT_RAM_7F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_00 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_01 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_02 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_03 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_04 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_05 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_06 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_07 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_08 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_09 = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_0A = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_0B = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_0C = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_0D = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_0E = 256'h0000000000000000000000000000000000000000000000000000000000000000;
parameter INITP_RAM_0F = 256'h0000000000000000000000000000000000000000000000000000000000000000;
input CLKA, CEA, CLKB, CEB;
input OCE; 
input RESET; 
input [8:0] ADA, ADB;
input [63:0] DI;
input [7:0] DIP;
input [2:0] BLKSELA, BLKSELB;
input DECCI, SECCI;
output [63:0] DO;
output [7:0] DOP;
output DECCO, SECCO;
output [7:0] ECCP;
endmodule


module MULTADDALU12X12 (...);
parameter A0REG_CLK = "BYPASS"; 
parameter A0REG_CE = "CE0"; 
parameter A0REG_RESET = "RESET0"; 
parameter A1REG_CLK = "BYPASS"; 
parameter A1REG_CE = "CE0"; 
parameter A1REG_RESET = "RESET0"; 
parameter B0REG_CLK = "BYPASS"; 
parameter B0REG_CE = "CE0"; 
parameter B0REG_RESET = "RESET0"; 
parameter B1REG_CLK = "BYPASS"; 
parameter B1REG_CE = "CE0"; 
parameter B1REG_RESET = "RESET0"; 
parameter ACCSEL_IREG_CLK = "BYPASS"; 
parameter ACCSEL_IREG_CE = "CE0"; 
parameter ACCSEL_IREG_RESET = "RESET0"; 
parameter CASISEL_IREG_CLK = "BYPASS"; 
parameter CASISEL_IREG_CE = "CE0"; 
parameter CASISEL_IREG_RESET = "RESET0"; 
parameter ADDSUB0_IREG_CLK = "BYPASS"; 
parameter ADDSUB0_IREG_CE = "CE0"; 
parameter ADDSUB0_IREG_RESET = "RESET0"; 
parameter ADDSUB1_IREG_CLK = "BYPASS"; 
parameter ADDSUB1_IREG_CE = "CE0"; 
parameter ADDSUB1_IREG_RESET = "RESET0"; 
parameter PREG0_CLK = "BYPASS"; 
parameter PREG0_CE = "CE0"; 
parameter PREG0_RESET = "RESET0"; 
parameter PREG1_CLK = "BYPASS"; 
parameter PREG1_CE = "CE0"; 
parameter PREG1_RESET = "RESET0"; 
parameter FB_PREG_EN = "FALSE"; 
parameter ACCSEL_PREG_CLK = "BYPASS"; 
parameter ACCSEL_PREG_CE = "CE0"; 
parameter ACCSEL_PREG_RESET = "RESET0"; 
parameter CASISEL_PREG_CLK = "BYPASS"; 
parameter CASISEL_PREG_CE = "CE0"; 
parameter CASISEL_PREG_RESET = "RESET0"; 
parameter ADDSUB0_PREG_CLK = "BYPASS"; 
parameter ADDSUB0_PREG_CE = "CE0"; 
parameter ADDSUB0_PREG_RESET = "RESET0"; 
parameter ADDSUB1_PREG_CLK = "BYPASS"; 
parameter ADDSUB1_PREG_CE = "CE0"; 
parameter ADDSUB1_PREG_RESET = "RESET0"; 
parameter OREG_CLK = "BYPASS"; 
parameter OREG_CE = "CE0"; 
parameter OREG_RESET = "RESET0"; 
parameter MULT_RESET_MODE = "SYNC";
parameter PRE_LOAD = 48'h000000000000;
parameter DYN_ADD_SUB_0 = "FALSE";
parameter ADD_SUB_0 = 1'b0;
parameter DYN_ADD_SUB_1 = "FALSE";
parameter ADD_SUB_1 = 1'b0;
parameter DYN_CASI_SEL = "FALSE";
parameter CASI_SEL = 1'b0;
parameter DYN_ACC_SEL = "FALSE";
parameter ACC_SEL = 1'b0;
output [47:0] DOUT, CASO;
input  [11:0] A0, B0, A1, B1;
input  [47:0] CASI;
input  ACCSEL;
input  CASISEL;
input  [1:0] ADDSUB;
input  [1:0] CLK, CE, RESET;
endmodule

module MULTALU27X18 (...);
parameter AREG_CLK = "BYPASS"; 
parameter AREG_CE = "CE0"; 
parameter AREG_RESET = "RESET0"; 
parameter BREG_CLK = "BYPASS"; 
parameter BREG_CE = "CE0"; 
parameter BREG_RESET = "RESET0"; 
parameter DREG_CLK = "BYPASS"; 
parameter DREG_CE = "CE0"; 
parameter DREG_RESET = "RESET0"; 
parameter C_IREG_CLK = "BYPASS"; 
parameter C_IREG_CE = "CE0"; 
parameter C_IREG_RESET = "RESET0"; 
parameter PSEL_IREG_CLK = "BYPASS"; 
parameter PSEL_IREG_CE = "CE0"; 
parameter PSEL_IREG_RESET = "RESET0"; 
parameter PADDSUB_IREG_CLK = "BYPASS"; 
parameter PADDSUB_IREG_CE = "CE0"; 
parameter PADDSUB_IREG_RESET = "RESET0"; 
parameter ADDSUB0_IREG_CLK = "BYPASS"; 
parameter ADDSUB0_IREG_CE = "CE0"; 
parameter ADDSUB0_IREG_RESET = "RESET0"; 
parameter ADDSUB1_IREG_CLK = "BYPASS"; 
parameter ADDSUB1_IREG_CE = "CE0"; 
parameter ADDSUB1_IREG_RESET = "RESET0"; 
parameter CSEL_IREG_CLK = "BYPASS"; 
parameter CSEL_IREG_CE = "CE0"; 
parameter CSEL_IREG_RESET = "RESET0"; 
parameter CASISEL_IREG_CLK = "BYPASS"; 
parameter CASISEL_IREG_CE = "CE0"; 
parameter CASISEL_IREG_RESET = "RESET0"; 
parameter ACCSEL_IREG_CLK = "BYPASS"; 
parameter ACCSEL_IREG_CE = "CE0"; 
parameter ACCSEL_IREG_RESET = "RESET0"; 
parameter PREG_CLK = "BYPASS"; 
parameter PREG_CE = "CE0"; 
parameter PREG_RESET = "RESET0"; 
parameter ADDSUB0_PREG_CLK = "BYPASS"; 
parameter ADDSUB0_PREG_CE = "CE0"; 
parameter ADDSUB0_PREG_RESET = "RESET0"; 
parameter ADDSUB1_PREG_CLK = "BYPASS"; 
parameter ADDSUB1_PREG_CE = "CE0"; 
parameter ADDSUB1_PREG_RESET = "RESET0"; 
parameter CSEL_PREG_CLK = "BYPASS"; 
parameter CSEL_PREG_CE = "CE0"; 
parameter CSEL_PREG_RESET = "RESET0"; 
parameter CASISEL_PREG_CLK = "BYPASS"; 
parameter CASISEL_PREG_CE = "CE0"; 
parameter CASISEL_PREG_RESET = "RESET0"; 
parameter ACCSEL_PREG_CLK = "BYPASS"; 
parameter ACCSEL_PREG_CE = "CE0"; 
parameter ACCSEL_PREG_RESET = "RESET0"; 
parameter C_PREG_CLK = "BYPASS"; 
parameter C_PREG_CE = "CE0"; 
parameter C_PREG_RESET = "RESET0"; 
parameter FB_PREG_EN = "FALSE"; 
parameter SOA_PREG_EN = "FALSE"; 
parameter OREG_CLK = "BYPASS"; 
parameter OREG_CE = "CE0"; 
parameter OREG_RESET = "RESET0"; 
parameter MULT_RESET_MODE = "SYNC";
parameter PRE_LOAD = 48'h000000000000;
parameter DYN_P_SEL = "FALSE";
parameter P_SEL = 1'b0;
parameter DYN_P_ADDSUB = "FALSE";
parameter P_ADDSUB = 1'b0;
parameter DYN_A_SEL = "FALSE";
parameter A_SEL = 1'b0;
parameter DYN_ADD_SUB_0 = "FALSE";
parameter ADD_SUB_0 = 1'b0;
parameter DYN_ADD_SUB_1 = "FALSE";
parameter ADD_SUB_1 = 1'b0;
parameter DYN_C_SEL = "FALSE";
parameter C_SEL = 1'b1;
parameter DYN_CASI_SEL = "FALSE";
parameter CASI_SEL = 1'b0;
parameter DYN_ACC_SEL = "FALSE";
parameter ACC_SEL = 1'b0;
parameter MULT12X12_EN = "FALSE";
output [47:0] DOUT, CASO;
output [26:0] SOA;
input  [26:0] A, SIA;
input  [17:0] B;
input  [47:0] C;
input  [25:0] D;
input  [47:0] CASI;
input  ACCSEL;
input  PSEL;
input  ASEL;
input  PADDSUB;
input  CSEL, CASISEL;
input  [1:0] ADDSUB;
input  [1:0] CLK, CE, RESET;
endmodule

module MULT12X12 (...);
parameter AREG_CLK = "BYPASS"; 
parameter AREG_CE = "CE0"; 
parameter AREG_RESET = "RESET0"; 
parameter BREG_CLK = "BYPASS"; 
parameter BREG_CE = "CE0"; 
parameter BREG_RESET = "RESET0"; 
parameter PREG_CLK = "BYPASS"; 
parameter PREG_CE = "CE0"; 
parameter PREG_RESET = "RESET0"; 
parameter OREG_CLK = "BYPASS"; 
parameter OREG_CE = "CE0"; 
parameter OREG_RESET = "RESET0"; 
parameter MULT_RESET_MODE = "SYNC";
output [23:0] DOUT;
input  [11:0] A, B;
input  [1:0] CLK, CE, RESET;
endmodule

module MULT27X36 (...);
parameter AREG_CLK = "BYPASS"; 
parameter AREG_CE = "CE0"; 
parameter AREG_RESET = "RESET0"; 
parameter BREG_CLK = "BYPASS"; 
parameter BREG_CE = "CE0"; 
parameter BREG_RESET = "RESET0"; 
parameter DREG_CLK = "BYPASS"; 
parameter DREG_CE = "CE0"; 
parameter DREG_RESET = "RESET0"; 
parameter PADDSUB_IREG_CLK = "BYPASS"; 
parameter PADDSUB_IREG_CE = "CE0"; 
parameter PADDSUB_IREG_RESET = "RESET0"; 
parameter PREG_CLK = "BYPASS"; 
parameter PREG_CE = "CE0"; 
parameter PREG_RESET = "RESET0"; 
parameter PSEL_IREG_CLK = "BYPASS"; 
parameter PSEL_IREG_CE = "CE0"; 
parameter PSEL_IREG_RESET = "RESET0"; 
parameter OREG_CLK = "BYPASS"; 
parameter OREG_CE = "CE0"; 
parameter OREG_RESET = "RESET0"; 
parameter MULT_RESET_MODE = "SYNC";
parameter DYN_P_SEL = "FALSE";
parameter P_SEL = 1'b0;
parameter DYN_P_ADDSUB = "FALSE";
parameter P_ADDSUB = 1'b0;
output [62:0] DOUT;
input  [26:0] A;
input  [35:0] B;
input  [25:0] D;
input  [1:0] CLK, CE, RESET;
input  PSEL;
input  PADDSUB;
endmodule

module IDDR_MEM (...);
input D, ICLK, PCLK;
input [2:0] WADDR;
input [2:0] RADDR;
input RESET;
output  Q0,Q1;
endmodule


module ODDR_MEM (...);
parameter TCLK_SOURCE = "DQSW"; 
parameter TXCLK_POL = 1'b0; 
input D0, D1;
input TX, PCLK, TCLK, RESET;
output  Q0, Q1;
endmodule


module IDES4_MEM (...);
input PCLK, D, ICLK, FCLK, RESET, CALIB;
input [2:0] WADDR;
input [2:0] RADDR;
output Q0,Q1,Q2,Q3;
endmodule


module IDES8_MEM (...);
input PCLK, D, ICLK, FCLK, RESET, CALIB;
input [2:0] WADDR;
input [2:0] RADDR;
output Q0,Q1,Q2,Q3,Q4,Q5,Q6,Q7;
endmodule


module IDES14 (...);
input D, FCLK, PCLK, CALIB,RESET;
output Q0, Q1, Q2, Q3, Q4, Q5, Q6, Q7, Q8, Q9, Q10, Q11, Q12, Q13;
endmodule


module IDES32 (...);
input D, FCLK, PCLK, CALIB,RESET;
output Q0, Q1, Q2, Q3, Q4, Q5, Q6, Q7, Q8, Q9, Q10, Q11, Q12, Q13, Q14, Q15, Q16, Q17, Q18, Q19, Q20, Q21, Q22, Q23, Q24, Q25, Q26, Q27, Q28, Q29, Q30, Q31;
endmodule


module OSER4_MEM (...);
parameter HWL = "false";     
parameter TCLK_SOURCE = "DQSW"; 
parameter TXCLK_POL = 1'b0; 
input D0, D1, D2, D3;
input TX0, TX1;
input PCLK, FCLK, TCLK, RESET;
output  Q0,  Q1;
endmodule


module OSER8_MEM (...);
parameter HWL = "false";    
parameter TCLK_SOURCE = "DQSW"; 
parameter TXCLK_POL = 1'b0; 
input D0, D1, D2, D3, D4, D5, D6, D7;
input TX0, TX1, TX2, TX3;
input PCLK, FCLK, TCLK, RESET;
output  Q0,  Q1;
endmodule


module IODELAY (...);
parameter C_STATIC_DLY = 0; 
parameter DYN_DLY_EN = "FALSE";
parameter ADAPT_EN = "FALSE";
input  DI;
input  SDTAP;
input  VALUE;
input  [7:0] DLYSTEP;
output DF;
output DO;
endmodule


module OSIDES32 (...);
output [31:0] Q;
input D;
input PCLK, FCLKP, FCLKN, FCLKQP, FCLKQN;
input RESET;
output DF;
input  SDTAP;
input  VALUE;
input  [7:0] DLYSTEP;
parameter C_STATIC_DLY = 0; 
parameter DYN_DLY_EN = "FALSE";
parameter ADAPT_EN = "FALSE";
endmodule

module DCE (...);
input CLKIN;
input CE;
output CLKOUT;
endmodule

module DDRDLL (...);
input CLKIN;
input STOP;
input UPDNCNTL;
input RESET;
output [7:0]STEP;
output LOCK;
parameter DLL_FORCE = "FALSE";
parameter CODESCAL = "000";
parameter SCAL_EN = "TRUE";
parameter DIV_SEL = 1'b0; 
endmodule

module DLLDLY (...);
input CLKIN;
input [7:0] DLLSTEP, CSTEP;
input LOADN,MOVE;
output CLKOUT;
output FLAG;
parameter DLY_SIGN = 1'b0; 
parameter DLY_ADJ = 0; 
parameter DYN_DLY_EN = "FALSE";
parameter ADAPT_EN = "FALSE";
parameter STEP_SEL = 1'b0;
endmodule

module CLKDIV (...);
input HCLKIN;
input RESETN;
input CALIB;
output CLKOUT;
parameter DIV_MODE = "2"; 
endmodule

module CLKDIV2 (...);
input HCLKIN, RESETN;
output CLKOUT;
endmodule

module DHCE (...);
input CLKIN;
input CEN;
output CLKOUT;
endmodule

module OSCA (...);
parameter  FREQ_DIV = 100; 
output OSCOUT;
input OSCEN;
endmodule

module PLL (...);
input CLKIN;
input CLKFB;
input RESET;
input PLLPWD;
input RESET_I;
input RESET_O;
input [5:0] FBDSEL;
input [5:0] IDSEL;
input [6:0] MDSEL;
input [2:0] MDSEL_FRAC;
input [6:0] ODSEL0;
input [2:0] ODSEL0_FRAC;
input [6:0] ODSEL1;
input [6:0] ODSEL2;
input [6:0] ODSEL3;
input [6:0] ODSEL4;
input [6:0] ODSEL5;
input [6:0] ODSEL6;
input [3:0] DT0,DT1,DT2,DT3;
input [5:0] ICPSEL;
input [2:0] LPFRES;
input [1:0] LPFCAP;
input [2:0] PSSEL;
input PSDIR;
input PSPULSE;
input ENCLK0;
input ENCLK1;
input ENCLK2;
input ENCLK3;
input ENCLK4;
input ENCLK5;
input ENCLK6;
input SSCPOL;
input SSCON;
input [6:0] SSCMDSEL;
input [2:0] SSCMDSEL_FRAC;
output LOCK;
output CLKOUT0;
output CLKOUT1;
output CLKOUT2;
output CLKOUT3;
output CLKOUT4;
output CLKOUT5;
output CLKOUT6;
output CLKFBOUT;
parameter FCLKIN = "100.0"; 
parameter DYN_IDIV_SEL= "FALSE";
parameter IDIV_SEL = 1; 
parameter DYN_FBDIV_SEL= "FALSE";
parameter FBDIV_SEL = 1; 
parameter DYN_ODIV0_SEL= "FALSE";
parameter ODIV0_SEL = 8; 
parameter DYN_ODIV1_SEL= "FALSE";
parameter ODIV1_SEL = 8; 
parameter DYN_ODIV2_SEL= "FALSE";
parameter ODIV2_SEL = 8; 
parameter DYN_ODIV3_SEL= "FALSE";
parameter ODIV3_SEL = 8; 
parameter DYN_ODIV4_SEL= "FALSE";
parameter ODIV4_SEL = 8; 
parameter DYN_ODIV5_SEL= "FALSE";
parameter ODIV5_SEL = 8; 
parameter DYN_ODIV6_SEL= "FALSE";
parameter ODIV6_SEL = 8; 
parameter DYN_MDIV_SEL= "FALSE";
parameter MDIV_SEL = 8; 
parameter MDIV_FRAC_SEL = 0; 
parameter ODIV0_FRAC_SEL = 0; 
parameter CLKOUT0_EN = "TRUE";
parameter CLKOUT1_EN = "FALSE";
parameter CLKOUT2_EN = "FALSE";
parameter CLKOUT3_EN = "FALSE";
parameter CLKOUT4_EN = "FALSE";
parameter CLKOUT5_EN = "FALSE";
parameter CLKOUT6_EN = "FALSE";
parameter CLKFB_SEL = "INTERNAL"; 
parameter DYN_DT0_SEL = "FALSE"; 
parameter DYN_DT1_SEL = "FALSE"; 
parameter DYN_DT2_SEL = "FALSE"; 
parameter DYN_DT3_SEL = "FALSE"; 
parameter CLKOUT0_DT_DIR = 1'b1; 
parameter CLKOUT1_DT_DIR = 1'b1; 
parameter CLKOUT2_DT_DIR = 1'b1; 
parameter CLKOUT3_DT_DIR = 1'b1; 
parameter CLKOUT0_DT_STEP = 0; 
parameter CLKOUT1_DT_STEP = 0; 
parameter CLKOUT2_DT_STEP = 0; 
parameter CLKOUT3_DT_STEP = 0; 
parameter CLK0_IN_SEL = 1'b0;
parameter CLK0_OUT_SEL = 1'b0;
parameter CLK1_IN_SEL = 1'b0;
parameter CLK1_OUT_SEL = 1'b0;
parameter CLK2_IN_SEL = 1'b0;
parameter CLK2_OUT_SEL = 1'b0;
parameter CLK3_IN_SEL = 1'b0;
parameter CLK3_OUT_SEL = 1'b0;
parameter CLK4_IN_SEL = 2'b00;
parameter CLK4_OUT_SEL = 1'b0;
parameter CLK5_IN_SEL = 1'b0;
parameter CLK5_OUT_SEL = 1'b0;
parameter CLK6_IN_SEL = 1'b0;
parameter CLK6_OUT_SEL = 1'b0;
parameter DYN_DPA_EN = "FALSE";
parameter CLKOUT0_PE_COARSE = 0;
parameter CLKOUT0_PE_FINE = 0;
parameter CLKOUT1_PE_COARSE = 0;
parameter CLKOUT1_PE_FINE = 0;
parameter CLKOUT2_PE_COARSE = 0;
parameter CLKOUT2_PE_FINE = 0;
parameter CLKOUT3_PE_COARSE = 0;
parameter CLKOUT3_PE_FINE = 0;
parameter CLKOUT4_PE_COARSE = 0;
parameter CLKOUT4_PE_FINE = 0;
parameter CLKOUT5_PE_COARSE = 0;
parameter CLKOUT5_PE_FINE = 0;
parameter CLKOUT6_PE_COARSE = 0;
parameter CLKOUT6_PE_FINE = 0;
parameter DYN_PE0_SEL = "FALSE";
parameter DYN_PE1_SEL = "FALSE";
parameter DYN_PE2_SEL = "FALSE";
parameter DYN_PE3_SEL = "FALSE";
parameter DYN_PE4_SEL = "FALSE";
parameter DYN_PE5_SEL = "FALSE";
parameter DYN_PE6_SEL = "FALSE";
parameter DE0_EN = "FALSE";
parameter DE1_EN = "FALSE";
parameter DE2_EN = "FALSE";
parameter DE3_EN = "FALSE";
parameter DE4_EN = "FALSE";
parameter DE5_EN = "FALSE";
parameter DE6_EN = "FALSE";
parameter RESET_I_EN = "FALSE";
parameter RESET_O_EN =  "FALSE";
parameter DYN_ICP_SEL= "FALSE";
parameter ICP_SEL = 6'bXXXXXX;
parameter DYN_LPF_SEL= "FALSE";
parameter LPF_RES = 3'bXXX;
parameter LPF_CAP = 2'b00;
parameter SSC_EN = "FALSE";
endmodule

module PLLA (...);
input CLKIN;
input CLKFB;
input RESET;
input PLLPWD;
input RESET_I;
input RESET_O;
input [2:0] PSSEL;
input PSDIR;
input PSPULSE;
input SSCPOL;
input SSCON;
input [6:0] SSCMDSEL;
input [2:0] SSCMDSEL_FRAC;
input MDCLK;
input [1:0] MDOPC;
input MDAINC;
input [7:0] MDWDI;
output [7:0] MDRDO;
output LOCK;
output CLKOUT0;
output CLKOUT1;
output CLKOUT2;
output CLKOUT3;
output CLKOUT4;
output CLKOUT5;
output CLKOUT6;
output CLKFBOUT;
parameter FCLKIN = "100.0"; 
parameter IDIV_SEL = 1; 
parameter FBDIV_SEL = 1; 
parameter ODIV0_SEL = 8; 
parameter ODIV1_SEL = 8; 
parameter ODIV2_SEL = 8; 
parameter ODIV3_SEL = 8; 
parameter ODIV4_SEL = 8; 
parameter ODIV5_SEL = 8; 
parameter ODIV6_SEL = 8; 
parameter MDIV_SEL = 8; 
parameter MDIV_FRAC_SEL = 0; 
parameter ODIV0_FRAC_SEL = 0; 
parameter CLKOUT0_EN = "TRUE";
parameter CLKOUT1_EN = "FALSE";
parameter CLKOUT2_EN = "FALSE";
parameter CLKOUT3_EN = "FALSE";
parameter CLKOUT4_EN = "FALSE";
parameter CLKOUT5_EN = "FALSE";
parameter CLKOUT6_EN = "FALSE";
parameter CLKFB_SEL = "INTERNAL"; 
parameter CLKOUT0_DT_DIR = 1'b1; 
parameter CLKOUT1_DT_DIR = 1'b1; 
parameter CLKOUT2_DT_DIR = 1'b1; 
parameter CLKOUT3_DT_DIR = 1'b1; 
parameter CLKOUT0_DT_STEP = 0; 
parameter CLKOUT1_DT_STEP = 0; 
parameter CLKOUT2_DT_STEP = 0; 
parameter CLKOUT3_DT_STEP = 0; 
parameter CLK0_IN_SEL = 1'b0;
parameter CLK0_OUT_SEL = 1'b0;
parameter CLK1_IN_SEL = 1'b0;
parameter CLK1_OUT_SEL = 1'b0;
parameter CLK2_IN_SEL = 1'b0;
parameter CLK2_OUT_SEL = 1'b0;
parameter CLK3_IN_SEL = 1'b0;
parameter CLK3_OUT_SEL = 1'b0;
parameter CLK4_IN_SEL = 2'b00;
parameter CLK4_OUT_SEL = 1'b0;
parameter CLK5_IN_SEL = 1'b0;
parameter CLK5_OUT_SEL = 1'b0;
parameter CLK6_IN_SEL = 1'b0;
parameter CLK6_OUT_SEL = 1'b0;
parameter DYN_DPA_EN = "FALSE";
parameter CLKOUT0_PE_COARSE = 0;
parameter CLKOUT0_PE_FINE = 0;
parameter CLKOUT1_PE_COARSE = 0;
parameter CLKOUT1_PE_FINE = 0;
parameter CLKOUT2_PE_COARSE = 0;
parameter CLKOUT2_PE_FINE = 0;
parameter CLKOUT3_PE_COARSE = 0;
parameter CLKOUT3_PE_FINE = 0;
parameter CLKOUT4_PE_COARSE = 0;
parameter CLKOUT4_PE_FINE = 0;
parameter CLKOUT5_PE_COARSE = 0;
parameter CLKOUT5_PE_FINE = 0;
parameter CLKOUT6_PE_COARSE = 0;
parameter CLKOUT6_PE_FINE = 0;
parameter DYN_PE0_SEL = "FALSE";
parameter DYN_PE1_SEL = "FALSE";
parameter DYN_PE2_SEL = "FALSE";
parameter DYN_PE3_SEL = "FALSE";
parameter DYN_PE4_SEL = "FALSE";
parameter DYN_PE5_SEL = "FALSE";
parameter DYN_PE6_SEL = "FALSE";
parameter DE0_EN = "FALSE";
parameter DE1_EN = "FALSE";
parameter DE2_EN = "FALSE";
parameter DE3_EN = "FALSE";
parameter DE4_EN = "FALSE";
parameter DE5_EN = "FALSE";
parameter DE6_EN = "FALSE";
parameter RESET_I_EN = "FALSE";
parameter RESET_O_EN =  "FALSE";
parameter ICP_SEL = 6'bXXXXXX;
parameter LPF_RES = 3'bXXX;
parameter LPF_CAP = 2'b00;
parameter SSC_EN = "FALSE";
endmodule

module AE350_SOC (...);
input               POR_N;
input               HW_RSTN;
input               CORE_CLK;
input               DDR_CLK;
input               AHB_CLK;
input               APB_CLK;
input               DBG_TCK;
input               RTC_CLK;
input               CORE_CE;
input               AXI_CE;
input               DDR_CE;
input               AHB_CE;
input     [7:0]     APB_CE;
input               APB2AHB_CE;
input               SCAN_TEST;
input               SCAN_EN;
output              PRESETN;
output              HRESETN;
output              DDR_RSTN;
input       [15:0]  GP_INT;
input       [ 7:0]  DMA_REQ;
output      [ 7:0]  DMA_ACK;
output              CORE0_WFI_MODE;
input               WAKEUP_IN;           
output              RTC_WAKEUP;           
input               TEST_CLK;
input               TEST_MODE;
input               TEST_RSTN;
output  [31:0]      ROM_HADDR;
input   [31:0]      ROM_HRDATA;
input               ROM_HREADY;
input               ROM_HRESP;
output   [1:0]      ROM_HTRANS;
output              ROM_HWRITE;
output  [31:0]      APB_PADDR;
output              APB_PENABLE;
input   [31:0]      APB_PRDATA;
input               APB_PREADY;
output              APB_PSEL;
output  [31:0]      APB_PWDATA;
output              APB_PWRITE;
input               APB_PSLVERR;
output   [2:0]      APB_PPROT;
output   [3:0]      APB_PSTRB;
input   [31:0]      EXTS_HRDATA;
input               EXTS_HREADYIN;
input               EXTS_HRESP;
output  [31:0]      EXTS_HADDR;
output   [2:0]      EXTS_HBURST;
output   [3:0]      EXTS_HPROT;
output              EXTS_HSEL;
output   [2:0]      EXTS_HSIZE;
output   [1:0]      EXTS_HTRANS;
output  [31:0]      EXTS_HWDATA;
output              EXTS_HWRITE;
input   [31:0]      EXTM_HADDR;
input    [2:0]      EXTM_HBURST;
input    [3:0]      EXTM_HPROT;
output  [63:0]      EXTM_HRDATA;
input               EXTM_HREADY;
output              EXTM_HREADYOUT;
output              EXTM_HRESP;
input               EXTM_HSEL;
input    [2:0]      EXTM_HSIZE;
input    [1:0]      EXTM_HTRANS;
input   [63:0]      EXTM_HWDATA;
input               EXTM_HWRITE;
output  [31:0]      DDR_HADDR;
output   [2:0]      DDR_HBURST;
output   [3:0]      DDR_HPROT;
input   [63:0]      DDR_HRDATA;
input               DDR_HREADY;
input               DDR_HRESP;
output   [2:0]      DDR_HSIZE;
output   [1:0]      DDR_HTRANS;
output  [63:0]      DDR_HWDATA;
output              DDR_HWRITE;
input               TMS_IN;           
input               TRST_IN;
input               TDI_IN;
output              TDO_OUT;
output              TDO_OE;
input               SPI2_HOLDN_IN;
input               SPI2_WPN_IN;
input               SPI2_CLK_IN;
input               SPI2_CSN_IN;
input               SPI2_MISO_IN;
input               SPI2_MOSI_IN;
output              SPI2_HOLDN_OUT;
output              SPI2_HOLDN_OE;
output              SPI2_WPN_OUT;
output              SPI2_WPN_OE;
output              SPI2_CLK_OUT;
output              SPI2_CLK_OE;
output              SPI2_CSN_OUT;
output              SPI2_CSN_OE;
output              SPI2_MISO_OUT;
output              SPI2_MISO_OE;
output              SPI2_MOSI_OUT;
output              SPI2_MOSI_OE;
input               I2C_SCL_IN;
input               I2C_SDA_IN;
output              I2C_SCL;
output              I2C_SDA;
output              UART1_TXD;
output              UART1_RTSN;
input               UART1_RXD;
input               UART1_CTSN;
input               UART1_DSRN;
input               UART1_DCDN;
input               UART1_RIN;
output              UART1_DTRN;
output              UART1_OUT1N;
output              UART1_OUT2N;
output              UART2_TXD;
output              UART2_RTSN;
input               UART2_RXD;
input               UART2_CTSN;
input               UART2_DCDN;
input               UART2_DSRN;
input               UART2_RIN;
output              UART2_DTRN;
output              UART2_OUT1N;
output              UART2_OUT2N;
output              CH0_PWM;
output              CH0_PWMOE;
output              CH1_PWM;
output              CH1_PWMOE;
output              CH2_PWM;
output              CH2_PWMOE;
output              CH3_PWM;
output              CH3_PWMOE;
input     [31:0]    GPIO_IN;
output    [31:0]    GPIO_OE;
output    [31:0]    GPIO_OUT;
input	 [19:0]	SCAN_IN;
input		INTEG_TCK;
input		INTEG_TDI;
input		INTEG_TMS;
input		INTEG_TRST;
output		INTEG_TDO;
output   [19:0]	SCAN_OUT;
input		PGEN_CHAIN_I;
output		PRDYN_CHAIN_O;
input	[2:0]	EMA;
input	[1:0]	EMAW;
input		EMAS;
input		RET1N;
input		RET2N;
endmodule

module AE350_RAM (...);
input               POR_N;
input               HW_RSTN;
input               CORE_CLK;
input               AHB_CLK;
input               APB_CLK;
input               RTC_CLK;
input               CORE_CE;
input               AXI_CE;
input               AHB_CE;
input   [31:0]      EXTM_HADDR;
input    [2:0]      EXTM_HBURST;
input    [3:0]      EXTM_HPROT;
output  [63:0]      EXTM_HRDATA;
input               EXTM_HREADY;
output              EXTM_HREADYOUT;
output              EXTM_HRESP;
input               EXTM_HSEL;
input    [2:0]      EXTM_HSIZE;
input    [1:0]      EXTM_HTRANS;
input   [63:0]      EXTM_HWDATA;
input               EXTM_HWRITE;
input	[2:0]	EMA;
input	[1:0]	EMAW;
input		EMAS;
input		RET1N;
input		RET2N;
endmodule

module SAMB (...);
parameter MODE = 2'b00; 
input [23:0] SPIAD;
input LOAD;
input ADWSEL; 
endmodule

module OTP (...);
parameter MODE = 1'b0; 
input READ, SHIFT;
output DOUT;
endmodule

module CMSER (...);
output RUNNING;
output CRCERR;
output CRCDONE;
output ECCCORR;
output ECCUNCORR; 
output [27:0] ERRLOC;
output ECCDEC;
output DSRRD;
output DSRWR; 
output ASRRESET;
output ASRINC;
output REFCLK;
input CLK;
input [2:0] SEREN;
input ERRINJECT;
input [6:0] ERRINJLOC;
endmodule

module CMSERA (...);
output RUNNING;
output CRCERR;
output CRCDONE;
output ECCCORR;
output ECCUNCORR; 
output [26:0] ERR0LOC;
output [26:0] ERR1LOC;
output ECCDEC;
output DSRRD;
output DSRWR; 
output ASRRESET;
output ASRINC;
output REFCLK;
input CLK;
input [2:0] SEREN;
input ERR0INJECT,ERR1INJECT;
input [6:0] ERRINJ0LOC,ERRINJ1LOC;
endmodule

module ADCLRC (...);
endmodule

module ADCULC (...);
endmodule

module ADC (...);
endmodule

module MIPI_DPHY (...);
output RX_CLK_O, TX_CLK_O;
output [15:0] D0LN_HSRXD, D1LN_HSRXD, D2LN_HSRXD, D3LN_HSRXD;
output D0LN_HSRXD_VLD,D1LN_HSRXD_VLD,D2LN_HSRXD_VLD,D3LN_HSRXD_VLD;
input  D0LN_HSRX_DREN, D1LN_HSRX_DREN,  D2LN_HSRX_DREN,  D3LN_HSRX_DREN;
output  DI_LPRX0_N, DI_LPRX0_P, DI_LPRX1_N, DI_LPRX1_P, DI_LPRX2_N,  DI_LPRX2_P, DI_LPRX3_N, DI_LPRX3_P, DI_LPRXCK_N, DI_LPRXCK_P;
inout  CK_N, CK_P, D0_N, D0_P, D1_N, D1_P, D2_N, D2_P, D3_N, D3_P;
input HSRX_STOP, HSTXEN_LN0, HSTXEN_LN1, HSTXEN_LN2, HSTXEN_LN3, HSTXEN_LNCK,
input PWRON_RX, PWRON_TX, RESET, RX_CLK_1X, TX_CLK_1X;
input TXDPEN_LN0, TXDPEN_LN1, TXDPEN_LN2, TXDPEN_LN3, TXDPEN_LNCK, TXHCLK_EN;
input [15:0]  CKLN_HSTXD,D0LN_HSTXD,D1LN_HSTXD,D2LN_HSTXD,D3LN_HSTXD;
input HSTXD_VLD;
input CK0, CK90, CK180, CK270;
input DO_LPTX0_N, DO_LPTX1_N, DO_LPTX2_N, DO_LPTX3_N, DO_LPTXCK_N, DO_LPTX0_P, DO_LPTX1_P, DO_LPTX2_P, DO_LPTX3_P, DO_LPTXCK_P;
input HSRX_EN_CK, HSRX_EN_D0, HSRX_EN_D1, HSRX_EN_D2, HSRX_EN_D3, HSRX_ODTEN_CK, 
input RX_DRST_N, TX_DRST_N, WALIGN_DVLD;
output [7:0] MRDATA;
input MA_INC, MCLK;
input [1:0] MOPCODE;
input [7:0] MWDATA;
output  ALPEDO_LANE0, ALPEDO_LANE1, ALPEDO_LANE2, ALPEDO_LANE3, ALPEDO_LANECK;
output D1LN_DESKEW_DONE,D2LN_DESKEW_DONE,D3LN_DESKEW_DONE,D0LN_DESKEW_DONE;
output D1LN_DESKEW_ERROR, D2LN_DESKEW_ERROR, D3LN_DESKEW_ERROR, D0LN_DESKEW_ERROR;
input D0LN_DESKEW_REQ, D1LN_DESKEW_REQ, D2LN_DESKEW_REQ, D3LN_DESKEW_REQ;
input HSRX_DLYDIR_LANE0, HSRX_DLYDIR_LANE1, HSRX_DLYDIR_LANE2, HSRX_DLYDIR_LANE3, HSRX_DLYDIR_LANECK;
input HSRX_DLYLDN_LANE0, HSRX_DLYLDN_LANE1, HSRX_DLYLDN_LANE2, HSRX_DLYLDN_LANE3, HSRX_DLYLDN_LANECK;
input HSRX_DLYMV_LANE0, HSRX_DLYMV_LANE1,  HSRX_DLYMV_LANE2, HSRX_DLYMV_LANE3, HSRX_DLYMV_LANECK;
input  ALP_EDEN_LANE0, ALP_EDEN_LANE1, ALP_EDEN_LANE2, ALP_EDEN_LANE3, ALP_EDEN_LANECK, ALPEN_LN0, ALPEN_LN1, ALPEN_LN2, ALPEN_LN3, ALPEN_LNCK;
parameter        TX_PLLCLK = "NONE"; 
parameter        RX_ALIGN_BYTE                  = 8'b10111000 ; 
parameter        RX_HS_8BIT_MODE                = 1'b0 ; 
parameter        RX_LANE_ALIGN_EN               = 1'b0 ; 
parameter        TX_HS_8BIT_MODE                = 1'b0 ; 
parameter        HSREG_EN_LN0                   = 1'b0;  
parameter        HSREG_EN_LN1                   = 1'b0;  
parameter        HSREG_EN_LN2                   = 1'b0;  
parameter        HSREG_EN_LN3                   = 1'b0;  
parameter        HSREG_EN_LNCK                  = 1'b0;  
parameter        LANE_DIV_SEL                   = 2'b00;  
parameter        HSRX_EN                        = 1'b1 ;  
parameter        HSRX_LANESEL                   = 4'b1111 ;   
parameter        HSRX_LANESEL_CK                = 1'b1 ;   
parameter        HSTX_EN_LN0                    = 1'b0 ;   
parameter        HSTX_EN_LN1                    = 1'b0 ;   
parameter        HSTX_EN_LN2                    = 1'b0 ;   
parameter        HSTX_EN_LN3                    = 1'b0 ;   
parameter        HSTX_EN_LNCK                   = 1'b0 ;   
parameter        LPTX_EN_LN0                    = 1'b1 ;   
parameter        LPTX_EN_LN1                    = 1'b1 ;   
parameter        LPTX_EN_LN2                    = 1'b1 ;   
parameter        LPTX_EN_LN3                    = 1'b1 ;   
parameter        LPTX_EN_LNCK                   = 1'b1 ;   
parameter        TXDP_EN_LN0                    = 1'b0 ;  
parameter        TXDP_EN_LN1                    = 1'b0 ;   
parameter        TXDP_EN_LN2                    = 1'b0 ;   
parameter        TXDP_EN_LN3                    = 1'b0 ;   
parameter        TXDP_EN_LNCK                   = 1'b0 ;
parameter        CKLN_DELAY_EN                  = 1'b0;  
parameter        CKLN_DELAY_OVR_VAL             = 7'b0000000; 
parameter        D0LN_DELAY_EN                  = 1'b0;  
parameter        D0LN_DELAY_OVR_VAL             = 7'b0000000; 
parameter        D0LN_DESKEW_BYPASS             = 1'b0;  
parameter        D1LN_DELAY_EN                  = 1'b0;  
parameter        D1LN_DELAY_OVR_VAL             = 7'b0000000; 
parameter        D1LN_DESKEW_BYPASS             = 1'b0;  
parameter        D2LN_DELAY_EN                  = 1'b0;  
parameter        D2LN_DELAY_OVR_VAL             = 7'b0000000; 
parameter        D2LN_DESKEW_BYPASS             = 1'b0;  
parameter        D3LN_DELAY_EN                  = 1'b0;  
parameter        D3LN_DELAY_OVR_VAL             = 7'b0000000; 
parameter        D3LN_DESKEW_BYPASS             = 1'b0;  
parameter        DESKEW_EN_LOW_DELAY            = 1'b0;  
parameter        DESKEW_EN_ONE_EDGE             = 1'b0;  
parameter        DESKEW_FAST_LOOP_TIME          = 4'b0000;  
parameter        DESKEW_FAST_MODE               = 1'b0;  
parameter        DESKEW_HALF_OPENING            = 6'b010110; 
parameter        DESKEW_LSB_MODE                = 2'b00;  
parameter        DESKEW_M                       = 3'b011;  
parameter        DESKEW_M_TH                    = 13'b0000110100110; 
parameter        DESKEW_MAX_SETTING           = 7'b0100001; 
parameter        DESKEW_ONE_CLK_EDGE_EN       = 1'b0 ; 
parameter        DESKEW_RST_BYPASS              = 1'b0 ; 
parameter        RX_BYTE_LITTLE_ENDIAN          = 1'b1 ; 
parameter        RX_CLK_1X_SYNC_SEL             = 1'b0 ; 
parameter        RX_INVERT                      = 1'b0 ; 
parameter        RX_ONE_BYTE0_MATCH             = 1'b0 ; 
parameter        RX_RD_START_DEPTH              = 5'b00001; 
parameter        RX_SYNC_MODE                   = 1'b0 ; 
parameter        RX_WORD_ALIGN_BYPASS           = 1'b0 ; 
parameter        RX_WORD_ALIGN_DATA_VLD_SRC_SEL = 1'b0 ; 
parameter        RX_WORD_LITTLE_ENDIAN          = 1'b0 ; 
parameter        TX_BYPASS_MODE                 = 1'b0 ; 
parameter        TX_BYTECLK_SYNC_MODE           = 1'b0 ; 
parameter        TX_OCLK_USE_CIBCLK             = 1'b0 ; 
parameter        TX_RD_START_DEPTH              = 5'b00001; 
parameter        TX_SYNC_MODE                   = 1'b0 ; 
parameter        TX_WORD_LITTLE_ENDIAN          = 1'b1 ; 
parameter        EQ_CS_LANE0                    = 3'b100;  
parameter        EQ_CS_LANE1                    = 3'b100;  
parameter        EQ_CS_LANE2                    = 3'b100;  
parameter        EQ_CS_LANE3                    = 3'b100;  
parameter        EQ_CS_LANECK                   = 3'b100;  
parameter        EQ_RS_LANE0                    = 3'b100;  
parameter        EQ_RS_LANE1                    = 3'b100;  
parameter        EQ_RS_LANE2                    = 3'b100;  
parameter        EQ_RS_LANE3                    = 3'b100;  
parameter        EQ_RS_LANECK                   = 3'b100;  
parameter        HSCLK_LANE_LN0                 = 1'b0;  
parameter        HSCLK_LANE_LN1                 = 1'b0;  
parameter        HSCLK_LANE_LN2                 = 1'b0;  
parameter        HSCLK_LANE_LN3                 = 1'b0;  
parameter        HSCLK_LANE_LNCK                = 1'b1;  
parameter        ALP_ED_EN_LANE0                = 1'b1 ; 
parameter        ALP_ED_EN_LANE1                = 1'b1 ;  
parameter        ALP_ED_EN_LANE2                = 1'b1 ;  
parameter        ALP_ED_EN_LANE3                = 1'b1 ;  
parameter        ALP_ED_EN_LANECK               = 1'b1 ; 
parameter        ALP_ED_TST_LANE0               = 1'b0 ;  
parameter        ALP_ED_TST_LANE1               = 1'b0 ;  
parameter        ALP_ED_TST_LANE2               = 1'b0 ;  
parameter        ALP_ED_TST_LANE3               = 1'b0 ;  
parameter        ALP_ED_TST_LANECK              = 1'b0 ;  
parameter        ALP_EN_LN0                     = 1'b0 ; 
parameter        ALP_EN_LN1                     = 1'b0 ;  
parameter        ALP_EN_LN2                     = 1'b0 ;  
parameter        ALP_EN_LN3                     = 1'b0 ; 
parameter        ALP_EN_LNCK                    = 1'b0 ; 
parameter        ALP_HYS_EN_LANE0               = 1'b1 ;  
parameter        ALP_HYS_EN_LANE1               = 1'b1 ;  
parameter        ALP_HYS_EN_LANE2               = 1'b1 ;  
parameter        ALP_HYS_EN_LANE3               = 1'b1 ;  
parameter        ALP_HYS_EN_LANECK              = 1'b1 ;  
parameter        ALP_TH_LANE0                   = 4'b1000 ;   
parameter        ALP_TH_LANE1                   = 4'b1000 ;   
parameter        ALP_TH_LANE2                   = 4'b1000 ;   
parameter        ALP_TH_LANE3                   = 4'b1000 ;   
parameter        ALP_TH_LANECK                  = 4'b1000 ;   
parameter        ANA_BYTECLK_PH                 = 2'b00 ;  
parameter        BIT_REVERSE_LN0                = 1'b0 ;  
parameter        BIT_REVERSE_LN1                = 1'b0 ;  
parameter        BIT_REVERSE_LN2                = 1'b0 ;  
parameter        BIT_REVERSE_LN3                = 1'b0 ;  
parameter        BIT_REVERSE_LNCK               = 1'b0 ;  
parameter        BYPASS_TXHCLKEN                = 1'b1 ;  
parameter        BYPASS_TXHCLKEN_SYNC           = 1'b0 ;  
parameter        BYTE_CLK_POLAR                 = 1'b0 ;  
parameter        BYTE_REVERSE_LN0               = 1'b0 ;  
parameter        BYTE_REVERSE_LN1               = 1'b0 ;  
parameter        BYTE_REVERSE_LN2               = 1'b0 ;  
parameter        BYTE_REVERSE_LN3               = 1'b0 ;  
parameter        BYTE_REVERSE_LNCK              = 1'b0 ;  
parameter        EN_CLKB1X                      = 1'b1 ;  
parameter        EQ_PBIAS_LANE0                 = 4'b1000 ;   
parameter        EQ_PBIAS_LANE1                 = 4'b1000 ;   
parameter        EQ_PBIAS_LANE2                 = 4'b1000 ;   
parameter        EQ_PBIAS_LANE3                 = 4'b1000 ;   
parameter        EQ_PBIAS_LANECK                = 4'b1000 ;   
parameter        EQ_ZLD_LANE0                   = 4'b1000 ;   
parameter        EQ_ZLD_LANE1                   = 4'b1000 ;   
parameter        EQ_ZLD_LANE2                   = 4'b1000 ;   
parameter        EQ_ZLD_LANE3                   = 4'b1000 ;   
parameter        EQ_ZLD_LANECK                  = 4'b1000 ;   
parameter        HIGH_BW_LANE0                  = 1'b1 ;   
parameter        HIGH_BW_LANE1                  = 1'b1 ;   
parameter        HIGH_BW_LANE2                  = 1'b1 ;   
parameter        HIGH_BW_LANE3                  = 1'b1 ;   
parameter        HIGH_BW_LANECK                 = 1'b1 ;   
parameter        HSREG_VREF_CTL                 = 3'b100 ;   
parameter        HSREG_VREF_EN                  = 1'b1 ;  
parameter        HSRX_DLY_CTL_CK                = 7'b0000000 ;   
parameter        HSRX_DLY_CTL_LANE0             = 7'b0000000 ;   
parameter        HSRX_DLY_CTL_LANE1             = 7'b0000000 ;   
parameter        HSRX_DLY_CTL_LANE2             = 7'b0000000 ;   
parameter        HSRX_DLY_CTL_LANE3             = 7'b0000000 ;   
parameter        HSRX_DLY_SEL_LANE0             = 1'b0 ;  
parameter        HSRX_DLY_SEL_LANE1             = 1'b0 ;   
parameter        HSRX_DLY_SEL_LANE2             = 1'b0 ;   
parameter        HSRX_DLY_SEL_LANE3             = 1'b0 ;   
parameter        HSRX_DLY_SEL_LANECK            = 1'b0 ;   
parameter        HSRX_DUTY_LANE0                = 4'b1000 ;  
parameter        HSRX_DUTY_LANE1                = 4'b1000 ;  
parameter        HSRX_DUTY_LANE2                = 4'b1000 ;  
parameter        HSRX_DUTY_LANE3                = 4'b1000 ;  
parameter        HSRX_DUTY_LANECK               = 4'b1000 ;  
parameter        HSRX_EQ_EN_LANE0               = 1'b1 ;  
parameter        HSRX_EQ_EN_LANE1               = 1'b1 ;  
parameter        HSRX_EQ_EN_LANE2               = 1'b1 ;  
parameter        HSRX_EQ_EN_LANE3               = 1'b1 ;  
parameter        HSRX_EQ_EN_LANECK              = 1'b1 ;  
parameter        HSRX_IBIAS                     = 4'b0011 ;   
parameter        HSRX_IBIAS_TEST_EN             = 1'b0 ;   
parameter        HSRX_IMARG_EN                  = 1'b0 ;   
parameter        HSRX_ODT_EN                    = 1'b1 ;   
parameter        HSRX_ODT_TST                   = 4'b0000 ;   
parameter        HSRX_ODT_TST_CK                = 1'b0 ;   
parameter        HSRX_SEL                       = 4'b0000 ;   
parameter        HSRX_STOP_EN                   = 1'b0 ; 
parameter        HSRX_TST                       = 4'b0000 ;   
parameter        HSRX_TST_CK                    = 1'b0 ;   
parameter        HSRX_WAIT4EDGE                 = 1'b1 ;   
parameter        HYST_NCTL                      = 2'b01 ;  
parameter        HYST_PCTL                      = 2'b01 ;  
parameter        IBIAS_TEST_EN                  = 1'b0 ;   
parameter        LB_CH_SEL                      = 1'b0 ;   
parameter        LB_EN_LN0                      = 1'b0 ;   
parameter        LB_EN_LN1                      = 1'b0 ;   
parameter        LB_EN_LN2                      = 1'b0 ;   
parameter        LB_EN_LN3                      = 1'b0 ;   
parameter        LB_EN_LNCK                     = 1'b0 ;   
parameter        LB_POLAR_LN0                   = 1'b0 ;   
parameter        LB_POLAR_LN1                   = 1'b0 ;   
parameter        LB_POLAR_LN2                   = 1'b0 ;   
parameter        LB_POLAR_LN3                   = 1'b0 ;   
parameter        LB_POLAR_LNCK                  = 1'b0 ;   
parameter        LOW_LPRX_VTH                   = 1'b0 ;   
parameter        LPBK_DATA2TO1                  = 4'b0000;  
parameter        LPBK_DATA2TO1_CK               = 1'b0 ;   
parameter        LPBK_EN                        = 1'b0 ;   
parameter        LPBK_SEL                       = 4'b0000;  
parameter        LPBKTST_EN                     = 4'b0000;  
parameter        LPBKTST_EN_CK                  = 1'b0 ;   
parameter        LPRX_EN                        = 1'b1 ;   
parameter        LPRX_TST                       = 4'b0000;  
parameter        LPRX_TST_CK                    = 1'b0 ;   
parameter        LPTX_DAT_POLAR_LN0             = 1'b0 ;   
parameter        LPTX_DAT_POLAR_LN1             = 1'b0 ;   
parameter        LPTX_DAT_POLAR_LN2             = 1'b0 ;   
parameter        LPTX_DAT_POLAR_LN3             = 1'b0 ;   
parameter        LPTX_DAT_POLAR_LNCK            = 1'b0 ;   
parameter        LPTX_NIMP_LN0                  = 3'b100 ; 
parameter        LPTX_NIMP_LN1                  = 3'b100 ; 
parameter        LPTX_NIMP_LN2                  = 3'b100 ; 
parameter        LPTX_NIMP_LN3                  = 3'b100 ; 
parameter        LPTX_NIMP_LNCK                 = 3'b100 ; 
parameter        LPTX_PIMP_LN0                  = 3'b100 ; 
parameter        LPTX_PIMP_LN1                  = 3'b100 ; 
parameter        LPTX_PIMP_LN2                  = 3'b100 ; 
parameter        LPTX_PIMP_LN3                  = 3'b100 ; 
parameter        LPTX_PIMP_LNCK                 = 3'b100 ; 
parameter        MIPI_PMA_DIS_N                 = 1'b1 ;   
parameter        PGA_BIAS_LANE0                 = 4'b1000 ;   
parameter        PGA_BIAS_LANE1                 = 4'b1000 ;   
parameter        PGA_BIAS_LANE2                 = 4'b1000 ;   
parameter        PGA_BIAS_LANE3                 = 4'b1000 ;   
parameter        PGA_BIAS_LANECK                = 4'b1000 ;   
parameter        PGA_GAIN_LANE0                 = 4'b1000 ;   
parameter        PGA_GAIN_LANE1                 = 4'b1000 ;   
parameter        PGA_GAIN_LANE2                 = 4'b1000 ;   
parameter        PGA_GAIN_LANE3                 = 4'b1000 ;   
parameter        PGA_GAIN_LANECK                = 4'b1000 ;   
parameter        RX_ODT_TRIM_LANE0              = 4'b1000 ;   
parameter        RX_ODT_TRIM_LANE1              = 4'b1000 ;   
parameter        RX_ODT_TRIM_LANE2              = 4'b1000 ;   
parameter        RX_ODT_TRIM_LANE3              = 4'b1000 ;   
parameter        RX_ODT_TRIM_LANECK             = 4'b1000 ;   
parameter        SLEWN_CTL_LN0                  = 4'b1111 ;  
parameter        SLEWN_CTL_LN1                  = 4'b1111 ;   
parameter        SLEWN_CTL_LN2                  = 4'b1111 ;   
parameter        SLEWN_CTL_LN3                  = 4'b1111 ;   
parameter        SLEWN_CTL_LNCK                 = 4'b1111 ;   
parameter        SLEWP_CTL_LN0                  = 4'b1111 ;   
parameter        SLEWP_CTL_LN1                  = 4'b1111 ;   
parameter        SLEWP_CTL_LN2                  = 4'b1111 ;   
parameter        SLEWP_CTL_LN3                  = 4'b1111 ;   
parameter        SLEWP_CTL_LNCK                 = 4'b1111 ;   
parameter        STP_UNIT                       = 2'b01 ;  
parameter        TERMN_CTL_LN0                  = 4'b1000 ;   
parameter        TERMN_CTL_LN1                  = 4'b1000 ;   
parameter        TERMN_CTL_LN2                  = 4'b1000 ;   
parameter        TERMN_CTL_LN3                  = 4'b1000 ;   
parameter        TERMN_CTL_LNCK                 = 4'b1000 ;   
parameter        TERMP_CTL_LN0                  = 4'b1000 ;   
parameter        TERMP_CTL_LN1                  = 4'b1000 ;   
parameter        TERMP_CTL_LN2                  = 4'b1000 ;   
parameter        TERMP_CTL_LN3                  = 4'b1000 ;   
parameter        TERMP_CTL_LNCK                 = 4'b1000 ;   
parameter        TEST_EN_LN0                    = 1'b0 ;   
parameter        TEST_EN_LN1                    = 1'b0 ;   
parameter        TEST_EN_LN2                    = 1'b0 ;   
parameter        TEST_EN_LN3                    = 1'b0 ;   
parameter        TEST_EN_LNCK                   = 1'b0 ;   
parameter        TEST_N_IMP_LN0                 = 1'b0 ;   
parameter        TEST_N_IMP_LN1                 = 1'b0 ;   
parameter        TEST_N_IMP_LN2                 = 1'b0 ;   
parameter        TEST_N_IMP_LN3                 = 1'b0 ;   
parameter        TEST_N_IMP_LNCK                = 1'b0 ;   
parameter        TEST_P_IMP_LN0                 = 1'b0 ;   
parameter        TEST_P_IMP_LN1                 = 1'b0 ;   
parameter        TEST_P_IMP_LN2                 = 1'b0 ;   
parameter        TEST_P_IMP_LN3                 = 1'b0 ;   
parameter        TEST_P_IMP_LNCK                = 1'b0 ;   
endmodule

module GTR12_QUAD (...);
endmodule

module GTR12_UPAR (...);
endmodule

module GTR12_PMAC (...);
endmodule

module DQS (...);
input DQSIN,PCLK,FCLK,RESET;
input [3:0] READ;
input [2:0] RCLKSEL;
input [7:0] DLLSTEP;
input [7:0] WSTEP;
input RLOADN, RMOVE, RDIR, WLOADN, WMOVE, WDIR, HOLD;
output DQSR90, DQSW0, DQSW270; 
output [2:0] RPOINT, WPOINT;
output RVALID,RBURST, RFLAG, WFLAG;
parameter FIFO_MODE_SEL = 1'b0; 
parameter RD_PNTR = 3'b000; 
parameter DQS_MODE = "X1"; 
parameter HWL = "false";   
endmodule

