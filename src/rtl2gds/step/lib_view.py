import logging
import os
import subprocess

from rtl2gds.global_configs import DEFAULT_SDC_FILE, R2G_PDK_DIR_IHP130, R2G_TOOL_DIR, StepN<PERSON>


def save_lib(
    top_name: str,
    netlist_file: str,
    lib_file_name: str,
    sdc_file: str = DEFAULT_SDC_FILE,
):
    artifacts = {
        "lib_file": lib_file_name,
        "log_file": f"{lib_file_name}.log",
        "metrics_file": f"{lib_file_name}.metrics",
    }

    shell_cmd = [
        "openroad",
        "-exit",
        "-no_splash",
        "-log",
        artifacts["log_file"],
        f"{R2G_TOOL_DIR}/write_lib.tcl",
    ]

    libs_ihp130 = f"{R2G_PDK_DIR_IHP130}/ihp-sg13g2/libs.ref/sg13g2_stdcell/lib/sg13g2_stdcell_typ_1p20V_25C.lib"
    tech_lef_ihp130 = f"{R2G_PDK_DIR_IHP130}/ihp-sg13g2/libs.ref/sg13g2_stdcell/lef/sg13g2_tech.lef"
    cell_lefs_ihp130 = (
        f"{R2G_PDK_DIR_IHP130}/ihp-sg13g2/libs.ref/sg13g2_stdcell/lef/sg13g2_stdcell.lef"
    )

    # Prepare environment variables
    step_env = {
        "PKD_LIBS": libs_ihp130,
        "TECH_LEF": tech_lef_ihp130,
        "CELL_LEFS": cell_lefs_ihp130,
        "NETLIST_FILE": netlist_file,
        "TOP_NAME": top_name,
        "OUT_LIB_FILE": lib_file_name,
        "SDC_FILE": sdc_file,
        "CLK_PORT_NAME": "clk",
        "CLK_FREQ_MHZ": "200",
    }

    step_env.update(os.environ.copy())

    logging.info(
        "(step.%s) \n subprocess cmd: %s \n subprocess env: %s",
        StepName.OR_STA_LIB,
        str(shell_cmd),
        str(step_env),
    )

    ret_code = subprocess.call(shell_cmd, env=step_env)
    if ret_code != 0:
        raise subprocess.CalledProcessError(ret_code, shell_cmd)

    metrics = {}

    return metrics, artifacts


if __name__ == "__main__":

    r2g_base = "/opt/rtl2gds"

    save_lib(
        top_name="gcd",
        netlist_file=f"{r2g_base}/design_zoo/gcd/gcd_results/gcd_filler.v",
        lib_file_name=f"{r2g_base}/design_zoo/merge_result/gcd.lib",
    )
