#!/usr/bin/env python3
"""
Standalone test for TaskManager functionality.
"""

import threading
from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel


# Standalone TaskManager implementation for testing
class TaskStatus:
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class TaskInfo(BaseModel):
    task_id: str
    project_id: str
    step_name: str
    status: str
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


class TaskManager:
    def __init__(self, max_concurrent_tasks: int = 3):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.tasks: Dict[str, TaskInfo] = {}
        self.project_task_counts: Dict[str, int] = {}
        self.lock = threading.Lock()

    def get_running_task_count(self) -> int:
        with self.lock:
            return len([task for task in self.tasks.values() if task.status == TaskStatus.RUNNING])

    def get_project_task_count(self, project_id: str) -> int:
        with self.lock:
            return self.project_task_counts.get(project_id, 0)

    def can_start_new_task(self) -> bool:
        return self.get_running_task_count() < self.max_concurrent_tasks

    def add_task(self, task_info: TaskInfo) -> bool:
        with self.lock:
            if not self.can_start_new_task():
                return False

            self.tasks[task_info.task_id] = task_info
            self.project_task_counts[task_info.project_id] = (
                self.project_task_counts.get(task_info.project_id, 0) + 1
            )
            return True

    def start_task(self, task_id: str):
        with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id].status = TaskStatus.RUNNING
                self.tasks[task_id].started_at = datetime.now()

    def complete_task(self, task_id: str, success: bool = True, error_message: str = None):
        with self.lock:
            if task_id in self.tasks:
                task_info = self.tasks[task_id]
                task_info.status = TaskStatus.SUCCESS if success else TaskStatus.FAILED
                task_info.completed_at = datetime.now()
                if error_message:
                    task_info.error_message = error_message

                # Decrement project task count
                if task_info.project_id in self.project_task_counts:
                    self.project_task_counts[task_info.project_id] -= 1
                    if self.project_task_counts[task_info.project_id] <= 0:
                        del self.project_task_counts[task_info.project_id]

    def get_task(self, task_id: str) -> Optional[TaskInfo]:
        with self.lock:
            return self.tasks.get(task_id)

    def get_all_tasks(self) -> List[TaskInfo]:
        with self.lock:
            return list(self.tasks.values())

    def get_project_tasks(self, project_id: str) -> List[TaskInfo]:
        with self.lock:
            return [task for task in self.tasks.values() if task.project_id == project_id]


def test_task_manager_functionality():
    """Test TaskManager class functionality."""
    print("🧪 Testing TaskManager functionality...")

    # Create task manager with max 2 concurrent tasks
    manager = TaskManager(max_concurrent_tasks=2)

    # Test initial state
    assert manager.get_running_task_count() == 0
    assert manager.can_start_new_task() == True
    assert manager.get_project_task_count("project1") == 0
    print("✅ Initial state tests passed")

    # Add first task
    task1 = TaskInfo(
        task_id="task1",
        project_id="project1",
        step_name="synthesis",
        status=TaskStatus.PENDING,
        created_at=datetime.now(),
    )
    assert manager.add_task(task1) == True
    assert manager.get_project_task_count("project1") == 1
    print("✅ First task addition tests passed")

    # Start the task
    manager.start_task("task1")
    assert manager.get_running_task_count() == 1
    assert manager.can_start_new_task() == True
    print("✅ Task start tests passed")

    # Add second task
    task2 = TaskInfo(
        task_id="task2",
        project_id="project2",
        step_name="floorplan",
        status=TaskStatus.PENDING,
        created_at=datetime.now(),
    )
    assert manager.add_task(task2) == True
    manager.start_task("task2")
    assert manager.get_running_task_count() == 2
    assert manager.can_start_new_task() == False
    print("✅ Second task and capacity limit tests passed")

    # Try to add third task (should fail)
    task3 = TaskInfo(
        task_id="task3",
        project_id="project1",
        step_name="placement",
        status=TaskStatus.PENDING,
        created_at=datetime.now(),
    )
    assert manager.add_task(task3) == False
    print("✅ Queue full rejection tests passed")

    # Complete first task
    manager.complete_task("task1", success=True)
    assert manager.get_running_task_count() == 1
    assert manager.can_start_new_task() == True
    assert manager.get_project_task_count("project1") == 0
    print("✅ Task completion tests passed")

    # Get task info
    retrieved_task = manager.get_task("task2")
    assert retrieved_task is not None
    assert retrieved_task.task_id == "task2"
    assert retrieved_task.status == TaskStatus.RUNNING
    print("✅ Task retrieval tests passed")

    # Complete second task with error
    manager.complete_task("task2", success=False, error_message="Test error")
    retrieved_task = manager.get_task("task2")
    assert retrieved_task.status == TaskStatus.FAILED
    assert retrieved_task.error_message == "Test error"
    assert manager.get_running_task_count() == 0
    print("✅ Task failure handling tests passed")

    # Test project task filtering
    task4 = TaskInfo(
        task_id="task4",
        project_id="project1",
        step_name="routing",
        status=TaskStatus.PENDING,
        created_at=datetime.now(),
    )
    manager.add_task(task4)
    project1_tasks = manager.get_project_tasks("project1")
    assert len(project1_tasks) == 2  # task1 (completed) and task4 (queued)
    assert all(task.project_id == "project1" for task in project1_tasks)
    print("✅ Project task filtering tests passed")

    # Test get all tasks
    all_tasks = manager.get_all_tasks()
    assert len(all_tasks) == 3  # task1, task2, task4
    print("✅ Get all tasks tests passed")

    print("🎉 All TaskManager tests passed!")


def test_concurrent_access():
    """Test thread safety of TaskManager."""
    print("🧪 Testing concurrent access...")

    import threading
    import time

    manager = TaskManager(max_concurrent_tasks=5)
    results = []

    def add_tasks(thread_id: int):
        for i in range(10):
            task = TaskInfo(
                task_id=f"thread{thread_id}_task{i}",
                project_id=f"project{thread_id}",
                step_name="synthesis",
                status=TaskStatus.PENDING,
                created_at=datetime.now(),
            )
            success = manager.add_task(task)
            results.append((thread_id, i, success))
            if success:
                manager.start_task(task.task_id)
                time.sleep(0.001)  # Small delay
                manager.complete_task(task.task_id, success=True)

    # Create multiple threads
    threads = []
    for i in range(3):
        thread = threading.Thread(target=add_tasks, args=(i,))
        threads.append(thread)

    # Start all threads
    for thread in threads:
        thread.start()

    # Wait for all threads to complete
    for thread in threads:
        thread.join()

    # Check results
    successful_adds = sum(1 for _, _, success in results if success)
    print(f"✅ Concurrent access test completed. Successful task additions: {successful_adds}")
    assert successful_adds > 0
    print("✅ Thread safety tests passed")


if __name__ == "__main__":
    test_task_manager_functionality()
    test_concurrent_access()
    print("\n🎉 All tests passed! TaskManager implementation is working correctly.")
