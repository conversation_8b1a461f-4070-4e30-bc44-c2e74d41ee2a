#!/usr/bin/env python3
"""Test script to verify the refactored integration between cloud.py and cloud_main.py"""

import logging
import sys
from pathlib import Path


def test_api_integration():
    """Test the API integration without running actual RTL2GDS flow."""
    logging.basicConfig(
        format="[%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d]: %(message)s",
        level=logging.INFO,
    )

    try:
        # Test that we can import the cloud module
        logging.info("Testing cloud.py imports...")
        from cloud import TaskStatus, _notify_task, app

        logging.info("✓ Cloud module imports successful")

        # Test notification function
        logging.info("Testing notification function...")
        test_result_files = {"test_file": "/tmp/test.gds"}
        _notify_task(
            result_files=test_result_files,
            status=TaskStatus.SUCCESS,
            task_id="test-123",
            task_type="TEST",
            project_id="test-project",
        )
        logging.info("✓ Notification function test completed")

        # Test that the FastAPI app is properly configured
        logging.info("Testing FastAPI app configuration...")
        routes = [route.path for route in app.routes]
        expected_route = "/apis/v1/ieda/stdin"
        if expected_route in routes:
            logging.info(f"✓ Expected API route {expected_route} found")
        else:
            logging.error(
                f"✗ Expected API route {expected_route} not found. Available routes: {routes}"
            )
            return False

        logging.info("All integration tests passed!")
        return True

    except Exception as e:
        logging.error(f"Integration test failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_api_integration()
    sys.exit(0 if success else 1)
