import asyncio
import logging
import os
import sys
import threading
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import uvicorn
import yaml
from fastapi import BackgroundTasks, FastAPI, HTTPException
from pydantic import BaseModel

from rtl2gds.global_configs import CLOUD_FLOW_STEPS, StepName

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

app = FastAPI()


# Task queue management
class TaskStatus:
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class TaskInfo(BaseModel):
    task_id: str
    project_id: str
    step_name: str
    status: str
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


class TaskManager:
    def __init__(self, max_concurrent_tasks: int = 3):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.tasks: Dict[str, TaskInfo] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.project_task_counts: Dict[str, int] = {}
        self.lock = threading.Lock()

    def get_running_task_count(self) -> int:
        with self.lock:
            return len([task for task in self.tasks.values() if task.status == TaskStatus.RUNNING])

    def get_project_task_count(self, project_id: str) -> int:
        with self.lock:
            return self.project_task_counts.get(project_id, 0)

    def can_start_new_task(self) -> bool:
        return self.get_running_task_count() < self.max_concurrent_tasks

    def add_task(self, task_info: TaskInfo) -> bool:
        with self.lock:
            if not self.can_start_new_task():
                return False

            self.tasks[task_info.task_id] = task_info
            self.project_task_counts[task_info.project_id] = (
                self.project_task_counts.get(task_info.project_id, 0) + 1
            )
            return True

    def start_task(self, task_id: str):
        with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id].status = TaskStatus.RUNNING
                self.tasks[task_id].started_at = datetime.now()

    def complete_task(self, task_id: str, success: bool = True, error_message: str = None):
        with self.lock:
            if task_id in self.tasks:
                task_info = self.tasks[task_id]
                task_info.status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
                task_info.completed_at = datetime.now()
                if error_message:
                    task_info.error_message = error_message

                # Decrement project task count
                if task_info.project_id in self.project_task_counts:
                    self.project_task_counts[task_info.project_id] -= 1
                    if self.project_task_counts[task_info.project_id] <= 0:
                        del self.project_task_counts[task_info.project_id]

                # Remove from running tasks
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]

    def get_task(self, task_id: str) -> Optional[TaskInfo]:
        with self.lock:
            return self.tasks.get(task_id)

    def get_all_tasks(self) -> List[TaskInfo]:
        with self.lock:
            return list(self.tasks.values())

    def get_project_tasks(self, project_id: str) -> List[TaskInfo]:
        with self.lock:
            return [task for task in self.tasks.values() if task.project_id == project_id]


# Global task manager instance
MAX_CONCURRENT_TASKS = int(os.getenv("MAX_CONCURRENT_TASKS", "3"))
task_manager = TaskManager(max_concurrent_tasks=MAX_CONCURRENT_TASKS)


# API Models
class StdinEDA(BaseModel):
    code: str
    ownerID: str
    projectID: str
    userID: str
    filePath: str | None
    parameters: dict


class ResponseData(BaseModel):
    code: str


class ResponseModel(BaseModel):
    code: int
    message: str
    data: ResponseData | None


class TaskSubmissionResponse(BaseModel):
    task_id: str
    status: str
    message: str


class TaskStatusResponse(BaseModel):
    task_id: str
    project_id: str
    step_name: str
    status: str
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


class TaskListResponse(BaseModel):
    tasks: List[TaskStatusResponse]
    total_running: int
    max_concurrent: int


async def run_rtl2gds_task(task_id: str, stdin: StdinEDA) -> None:
    """
    Background task to run RTL2GDS cloud flow.
    """
    try:
        task_manager.start_task(task_id)
        logging.info(f"Starting RTL2GDS task {task_id} for project {stdin.projectID}")

        step_name = stdin.code
        mount_point = Path("/projectData")
        eda_workspace = mount_point / stdin.projectID
        cloud_config = eda_workspace / "config.yaml"
        rtl_file = eda_workspace / "top.v"

        # Execute the RTL2GDS cloud main process
        python_executable = sys.executable
        cloud_main_path = Path(__file__).parent.parent.parent / "src" / "rtl2gds" / "cloud_main.py"

        cmd = [
            python_executable,
            str(cloud_main_path),
            str(rtl_file),
            str(cloud_config),
            str(eda_workspace),
            step_name,
        ]

        logging.info(f"Executing command: {' '.join(cmd)}")

        # Run the subprocess
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            env={**os.environ, "TASK_ID": task_id},
        )

        stdout, stderr = await process.communicate()

        if process.returncode == 0:
            logging.info(f"Task {task_id} completed successfully")
            logging.debug(f"Task {task_id} stdout: {stdout.decode()}")
            task_manager.complete_task(task_id, success=True)
        else:
            error_msg = (
                f"Process failed with return code {process.returncode}. stderr: {stderr.decode()}"
            )
            logging.error(f"Task {task_id} failed: {error_msg}")
            task_manager.complete_task(task_id, success=False, error_message=error_msg)

    except Exception as e:
        error_msg = f"Unexpected error in background task: {str(e)}"
        logging.exception(f"Task {task_id} failed with exception: {error_msg}")
        task_manager.complete_task(task_id, success=False, error_message=error_msg)


@app.post("/apis/v1/ieda/stdin", response_model=TaskSubmissionResponse)
async def call_ieda(stdin: StdinEDA, background_tasks: BackgroundTasks) -> TaskSubmissionResponse:
    """
    Handles requests to trigger RTL2GDS cloud flow steps with task queue management.
    """
    logging.info(f"Received request: {stdin}")

    try:
        step_name = stdin.code
        if step_name not in CLOUD_FLOW_STEPS:
            logging.warning(f"Invalid step name: {step_name}")
            raise HTTPException(status_code=400, detail=f"Invalid step name: {step_name}")

        # Check if we can start a new task
        if not task_manager.can_start_new_task():
            current_running = task_manager.get_running_task_count()
            raise HTTPException(
                status_code=429,
                detail=f"Task queue is full. Currently running {current_running}/{task_manager.max_concurrent_tasks} tasks. Please try again later.",
            )

        mount_point = Path("/projectData")
        if not mount_point.is_dir():
            logging.error(
                f"mount point {mount_point} not found or is not a directory inside the pod."
            )
            raise HTTPException(
                status_code=503, detail="Service Unavailable: Storage system not accessible"
            )

        eda_workspace = mount_point / stdin.projectID  # /projectData/<projectID>
        cloud_config = eda_workspace / "config.yaml"  # /projectData/<projectID>/config.yaml
        rtl_file = eda_workspace / "top.v"  # /projectData/<projectID>/top.v

        if not eda_workspace.is_dir():
            logging.error(f"Eda workspace {eda_workspace} not found or is not a directory.")
            raise HTTPException(status_code=404, detail="Not Found: EDA workspace does not exist")
        if not cloud_config.is_file():
            logging.warning(f"Config file not found at: {cloud_config}")
            raise HTTPException(status_code=404, detail="Not Found: Configuration file is missing")
        if not rtl_file.is_file():
            logging.warning(f"RTL file not found at: {rtl_file}")
            raise HTTPException(status_code=404, detail="Not Found: RTL file is missing")

        validation_response = is_valid_config(cloud_config, step_name)
        if validation_response.code != 0:
            logging.warning(f"Configuration validation failed: {validation_response.message}")
            raise HTTPException(
                status_code=validation_response.code, detail=validation_response.message
            )

        # Create task
        task_id = str(uuid.uuid4())
        task_info = TaskInfo(
            task_id=task_id,
            project_id=stdin.projectID,
            step_name=step_name,
            status=TaskStatus.QUEUED,
            created_at=datetime.now(),
        )

        # Add task to queue
        if not task_manager.add_task(task_info):
            raise HTTPException(
                status_code=429, detail="Failed to add task to queue. Queue may be full."
            )

        # Start background task
        background_tasks.add_task(run_rtl2gds_task, task_id, stdin)

        logging.info(f"Task {task_id} queued successfully for project {stdin.projectID}")

        return TaskSubmissionResponse(
            task_id=task_id,
            status=TaskStatus.QUEUED,
            message=f"Task queued successfully. Task ID: {task_id}",
        )

    except HTTPException:
        raise
    except Exception as e:
        logging.exception(f"An unexpected error occurred in call_ieda: {e}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: Unexpected server error: {str(e)}"
        )


# New API endpoints for task management
@app.get("/apis/v1/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str) -> TaskStatusResponse:
    """
    Get the status of a specific task.
    """
    task_info = task_manager.get_task(task_id)
    if not task_info:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

    return TaskStatusResponse(
        task_id=task_info.task_id,
        project_id=task_info.project_id,
        step_name=task_info.step_name,
        status=task_info.status,
        created_at=task_info.created_at,
        started_at=task_info.started_at,
        completed_at=task_info.completed_at,
        error_message=task_info.error_message,
    )


@app.get("/apis/v1/tasks", response_model=TaskListResponse)
async def get_all_tasks() -> TaskListResponse:
    """
    Get the status of all tasks.
    """
    all_tasks = task_manager.get_all_tasks()
    task_responses = [
        TaskStatusResponse(
            task_id=task.task_id,
            project_id=task.project_id,
            step_name=task.step_name,
            status=task.status,
            created_at=task.created_at,
            started_at=task.started_at,
            completed_at=task.completed_at,
            error_message=task.error_message,
        )
        for task in all_tasks
    ]

    return TaskListResponse(
        tasks=task_responses,
        total_running=task_manager.get_running_task_count(),
        max_concurrent=task_manager.max_concurrent_tasks,
    )


@app.get("/apis/v1/projects/{project_id}/tasks", response_model=TaskListResponse)
async def get_project_tasks(project_id: str) -> TaskListResponse:
    """
    Get the status of all tasks for a specific project.
    """
    project_tasks = task_manager.get_project_tasks(project_id)
    task_responses = [
        TaskStatusResponse(
            task_id=task.task_id,
            project_id=task.project_id,
            step_name=task.step_name,
            status=task.status,
            created_at=task.created_at,
            started_at=task.started_at,
            completed_at=task.completed_at,
            error_message=task.error_message,
        )
        for task in project_tasks
    ]

    return TaskListResponse(
        tasks=task_responses,
        total_running=task_manager.get_running_task_count(),
        max_concurrent=task_manager.max_concurrent_tasks,
    )


@app.get("/apis/v1/queue/status")
async def get_queue_status():
    """
    Get current queue status and statistics.
    """
    return {
        "total_running": task_manager.get_running_task_count(),
        "max_concurrent": task_manager.max_concurrent_tasks,
        "available_slots": task_manager.max_concurrent_tasks
        - task_manager.get_running_task_count(),
        "project_task_counts": dict(task_manager.project_task_counts),
    }


def get_expected_step(finished_step: str) -> str | None:
    try:
        index = CLOUD_FLOW_STEPS.index(finished_step)
        if index == len(CLOUD_FLOW_STEPS) - 1:
            return None
        return CLOUD_FLOW_STEPS[index + 1]
    except ValueError:
        return None


def is_valid_config(config_path: Path, step_name: str) -> ResponseModel:
    """Validates the configuration file."""
    logging.info(f"Validating config file: {config_path} for step: {step_name}")
    if not config_path.is_file():
        return ResponseModel(
            code=404,
            message="Not Found: Config file does not exist",
            data=ResponseData(code="CONFIG_FILE_NOT_FOUND"),
        )

    required_keys = ["TOP_NAME", "CLK_PORT_NAME"]

    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
            if config is None:
                logging.warning(f"Config file {config_path} is empty.")
                return ResponseModel(
                    code=400,
                    message="Bad Request: Config file is empty",
                    data=ResponseData(code="EMPTY_CONFIG"),
                )

        missing_keys = [key for key in required_keys if key not in config]
        if missing_keys:
            msg = f"Missing required keys in config: {', '.join(missing_keys)}"
            logging.warning(msg)
            return ResponseModel(
                code=400,
                message="Bad Request: Invalid configuration",
                data=ResponseData(code="MISSING_CONFIG_KEYS"),
            )

        if "FINISHED_STEP" in config:
            finished_step = config["FINISHED_STEP"]
            expected_next_step = get_expected_step(finished_step)
            logging.info(
                f"Config indicates finished step: {finished_step}. Expected next step: {expected_next_step}"
            )
            if step_name != StepName.RTL2GDS_ALL and expected_next_step != step_name:
                msg = f"Invalid step sequence. Expected {expected_next_step} after {finished_step}, got {step_name}"
                logging.warning(msg)
                return ResponseModel(
                    code=409,
                    message=f"Conflict: {msg}",
                    data=ResponseData(code="INVALID_STEP_SEQUENCE"),
                )
        elif step_name != StepName.RTL2GDS_ALL and step_name != StepName.SYNTHESIS:
            msg = f"Must start with {StepName.RTL2GDS_ALL} or {StepName.SYNTHESIS}"
            logging.warning(msg)
            return ResponseModel(
                code=400,
                message=f"Bad Request: Invalid starting step. {msg}",
                data=ResponseData(code="INVALID_INITIAL_STEP"),
            )

    except yaml.YAMLError as e:
        logging.error(f"Failed to parse YAML config {config_path}: {e}")
        return ResponseModel(
            code=400,
            message="Bad Request: Invalid YAML format",
            data=ResponseData(code="INVALID_YAML"),
        )
    except Exception as e:
        logging.exception(f"Unexpected error during config validation for {config_path}: {e}")
        return ResponseModel(
            code=500,
            message="Internal Server Error: Config validation failed",
            data=ResponseData(code="VALIDATION_ERROR"),
        )

    logging.info("Config validation successful.")
    return ResponseModel(code=0, message="OK: Config validation successful")


@app.get("/hello")
def get_hello():
    return {"Hello": "RTL2GDS Cloud YES!"}


if __name__ == "__main__":
    eda_service_host = os.getenv("EDA_SERVICE_HOST", "************")
    eda_service_port = 9444
    log_level = os.getenv("LOG_LEVEL", "info").lower()
    print(f"Starting RTL2GDS API server on {eda_service_host}:{eda_service_port}")
    uvicorn.run(
        app="cloud:app",
        host=eda_service_host,
        port=eda_service_port,
        log_level=log_level,
        reload=False,
    )
