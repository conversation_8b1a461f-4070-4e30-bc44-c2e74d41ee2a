import logging
import os
import subprocess
import uuid
from pathlib import Path
from queue import Queue

import uvicorn
import yaml
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

from rtl2gds.global_configs import CLOUD_FLOW_STEPS, StepName

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

app = FastAPI()
I need you to rewrite the FastAPI Python script `design_zoo/gcd_cloud/cloud.py` to implement a task queue management system with the following specific requirements:

**Core Functionality:**
1. Create a REST API endpoint that accepts POST requests with a `projectID` parameter
2. When a POST request is received, start an RTL2GDS cloud flow process in the background
3. Implement a task queue system that tracks running tasks by `projectID`

**Task Queue Management:**
1. Maintain a running task counter/list that tracks active tasks per `projectID`
2. Increment the task count (+1) when a new task is created and started
3. Decrement the task count (-1) when a task completes successfully
4. Implement a maximum task limit - reject new task requests when the total number of running tasks exceeds this threshold

**API Requirements:**
1. POST endpoint to submit new RTL2GDS tasks with `projectID` parameter
2. GET endpoint(s) to query the current status of running tasks
3. Return appropriate HTTP status codes and error messages when rejecting tasks due to queue limits
4. Handle task completion detection and automatic cleanup

**Technical Specifications:**
- Use FastAPI framework
- Implement proper background task execution (consider using FastAPI's BackgroundTasks or asyncio)
- Include proper error handling and logging
- Define the maximum concurrent task limit as a configurable parameter
- Ensure thread-safe operations for the task counter/queue

Please examine the current `cloud.py` file first to understand the existing structure, then provide a complete rewrite that implements these requirements.

class StdinEDA(BaseModel):
    code: str
    ownerID: str
    projectID: str
    userID: str
    filePath: str | None
    parameter: dict


class ResponseData(BaseModel):
    code: str


class ResponseModel(BaseModel):
    code: int
    message: str
    data: ResponseData | None


@app.post("/apis/v1/ieda/stdin", response_model=ResponseModel)
async def call_ieda(stdin: StdinEDA) -> ResponseModel:
    """
    Handles requests to trigger RTL2GDS cloud flow steps.
    """
    logging.info(f"Received request: {stdin}")

    try:
        step_name = stdin.code
        if step_name not in CLOUD_FLOW_STEPS:
            logging.warning(f"Invalid step name: {step_name}")
            return ResponseModel(
                code=400,
                message=f"Invalid step name: {step_name}",
                data=ResponseData(code="INVALID_STEP_NAME"),
            )

        mount_point = Path("/projectData")
        if not mount_point.is_dir():
            logging.error(
                f"mount point {mount_point} not found or is not a directory inside the pod."
            )
            return ResponseModel(
                code=503,
                message="Service Unavailable: Storage system not accessible",
                data=ResponseData(code="STORAGE_UNAVAILABLE"),
            )

        parameters = stdin.parameters

        eda_workspace = mount_point / stdin.projectID  # /projectData/<projectID>
        cloud_config = eda_workspace / "config.yaml"  # /projectData/<projectID>/config.yaml
        rtl_file = eda_workspace / "top.v"  # /projectData/<projectID>/top.v

        if not eda_workspace.is_dir():
            logging.error(f"Eda workspace {eda_workspace} not found or is not a directory.")
            return ResponseModel(
                code=404,
                message="Not Found: EDA workspace does not exist",
                data=ResponseData(code="WORKSPACE_NOT_FOUND"),
            )
        if not cloud_config.is_file():
            logging.warning(f"Config file not found at: {cloud_config}")
            return ResponseModel(
                code=404,
                message="Not Found: Configuration file is missing",
                data=ResponseData(code="CONFIG_NOT_FOUND"),
            )
        if not rtl_file.is_file():
            logging.warning(f"RTL file not found at: {rtl_file}")
            return ResponseModel(
                code=404,
                message="Not Found: RTL file is missing",
                data=ResponseData(code="RTL_NOT_FOUND"),
            )

        validation_response = is_valid_config(cloud_config, step_name)
        if validation_response.code != 0:
            logging.warning(f"Configuration validation failed: {validation_response.message}")
            return validation_response

        logging.info("Configuration validated successfully. Starting Job...")
        response_message = start_r2g_job(
            job_name=f"rtl2gds-{stdin.projectID}-{step_name}-{uuid.uuid4().hex[:6]}",
            job_workspace_path=eda_workspace,
            job_config_path=cloud_config,
            job_rtl_path=rtl_file,
            flow_step=step_name,
            namespace=os.getenv("NAMESPACE", "eda"),
        )

        logging.info(f"Job creation response: {response_message}")

    except Exception as e:
        logging.exception(f"An unexpected error occurred in call_ieda: {e}")
        return ResponseModel(
            code=500,
            message=f"Internal Server Error: Unexpected server error: {e}",
            data=ResponseData(code="INTERNAL_ERROR"),
        )


def get_expected_step(finished_step: str) -> str | None:
    try:
        index = CLOUD_FLOW_STEPS.index(finished_step)
        if index == len(CLOUD_FLOW_STEPS) - 1:
            return None
        return CLOUD_FLOW_STEPS[index + 1]
    except ValueError:
        return None


def is_valid_config(config_path: Path, step_name: str) -> ResponseModel:
    """Validates the configuration file."""
    logging.info(f"Validating config file: {config_path} for step: {step_name}")
    if not config_path.is_file():
        return ResponseModel(
            code=404,
            message="Not Found: Config file does not exist",
            data=ResponseData(code="CONFIG_FILE_NOT_FOUND"),
        )

    required_keys = ["TOP_NAME", "CLK_PORT_NAME"]

    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
            if config is None:
                logging.warning(f"Config file {config_path} is empty.")
                return ResponseModel(
                    code=400,
                    message="Bad Request: Config file is empty",
                    data=ResponseData(code="EMPTY_CONFIG"),
                )

        missing_keys = [key for key in required_keys if key not in config]
        if missing_keys:
            msg = f"Missing required keys in config: {', '.join(missing_keys)}"
            logging.warning(msg)
            return ResponseModel(
                code=400,
                message="Bad Request: Invalid configuration",
                data=ResponseData(code="MISSING_CONFIG_KEYS"),
            )

        if "FINISHED_STEP" in config:
            finished_step = config["FINISHED_STEP"]
            expected_next_step = get_expected_step(finished_step)
            logging.info(
                f"Config indicates finished step: {finished_step}. Expected next step: {expected_next_step}"
            )
            if step_name != StepName.RTL2GDS_ALL and expected_next_step != step_name:
                msg = f"Invalid step sequence. Expected {expected_next_step} after {finished_step}, got {step_name}"
                logging.warning(msg)
                return ResponseModel(
                    code=409,
                    message=f"Conflict: {msg}",
                    data=ResponseData(code="INVALID_STEP_SEQUENCE"),
                )
        elif step_name != StepName.RTL2GDS_ALL and step_name != StepName.SYNTHESIS:
            msg = f"Must start with {StepName.RTL2GDS_ALL} or {StepName.SYNTHESIS}"
            logging.warning(msg)
            return ResponseModel(
                code=400,
                message=f"Bad Request: Invalid starting step. {msg}",
                data=ResponseData(code="INVALID_INITIAL_STEP"),
            )

    except yaml.YAMLError as e:
        logging.error(f"Failed to parse YAML config {config_path}: {e}")
        return ResponseModel(
            code=400,
            message="Bad Request: Invalid YAML format",
            data=ResponseData(code="INVALID_YAML"),
        )
    except Exception as e:
        logging.exception(f"Unexpected error during config validation for {config_path}: {e}")
        return ResponseModel(
            code=500,
            message="Internal Server Error: Config validation failed",
            data=ResponseData(code="VALIDATION_ERROR"),
        )

    logging.info("Config validation successful.")
    return ResponseModel(code=0, message="OK: Config validation successful")


@app.get("/hello")
def get_hello():
    return {"Hello": "RTL2GDS Cloud YES!"}


if __name__ == "__main__":
    eda_service_host = os.getenv("EDA_SERVICE_HOST", "************")
    eda_service_port = 9444
    log_level = os.getenv("LOG_LEVEL", "info").lower()
    print(f"Starting RTL2GDS API server on {eda_service_host}:{eda_service_port}")
    uvicorn.run(
        app="cloud:app",
        host=eda_service_host,
        port=eda_service_port,
        log_level=log_level,
        reload=False,
    )
