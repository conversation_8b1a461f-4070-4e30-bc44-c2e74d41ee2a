import asyncio
import logging
import os
import queue
import sys
import threading
from datetime import datetime
from pathlib import Path

import uvicorn
import yaml
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel

from rtl2gds.global_configs import CLOUD_FLOW_STEPS, StepName

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

app = FastAPI()


# Task queue management
class TaskStatus:
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class TaskInfo(BaseModel):
    task_id: str
    project_id: str
    step_name: str
    status: str
    created_at: datetime
    started_at: datetime | None = None
    completed_at: datetime | None = None
    error_message: str | None = None


class TaskManager:
    """
    Improved TaskManager with proper queue-based task scheduling.

    Uses queue.Queue for FIFO task processing and maintains task metadata
    in a dictionary for fast lookups.
    """

    def __init__(self, max_concurrent_tasks: int = 3):
        self.max_concurrent_tasks = max_concurrent_tasks

        # Task metadata storage (for fast lookups by task_id)
        self.tasks: dict[str, TaskInfo] = {}

        # Task execution data storage
        self.task_data: dict[str, any] = {}

        # FIFO queue for pending tasks (thread-safe)
        self.pending_queue: queue.Queue[str] = queue.Queue()

        # Currently running tasks
        self.running_tasks: set[str] = set()

        # Project task counts for tracking
        self.project_task_counts: dict[str, int] = {}

        # Thread safety
        self.lock = threading.Lock()

        # Background worker control
        self._worker_running = False
        self._worker_thread: threading.Thread | None = None

    def start_worker(self):
        """Start the background worker thread for processing queued tasks."""
        with self.lock:
            if not self._worker_running:
                self._worker_running = True
                self._worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
                self._worker_thread.start()
                logging.info("TaskManager worker thread started")

    def stop_worker(self):
        """Stop the background worker thread."""
        with self.lock:
            self._worker_running = False
        if self._worker_thread:
            self._worker_thread.join(timeout=5.0)
            logging.info("TaskManager worker thread stopped")

    def _worker_loop(self):
        """Background worker that processes queued tasks."""
        while self._worker_running:
            try:
                # Get next task from queue (blocks with timeout)
                task_id = self.pending_queue.get(timeout=1.0)

                with self.lock:
                    if (
                        task_id in self.tasks
                        and len(self.running_tasks) < self.max_concurrent_tasks
                    ):

                        # Move task from pending to running
                        self.running_tasks.add(task_id)
                        self.tasks[task_id].status = TaskStatus.RUNNING
                        self.tasks[task_id].started_at = datetime.now()

                        # Get task execution data
                        stdin_data = self.task_data.get(task_id)

                        logging.info(f"Worker started task {task_id}")

                        # Start the actual RTL2GDS process in a separate thread
                        if stdin_data:
                            import threading

                            execution_thread = threading.Thread(
                                target=self._execute_task_sync,
                                args=(task_id, stdin_data),
                                daemon=True,
                            )
                            execution_thread.start()

                    else:
                        # Put task back in queue if we can't process it now
                        self.pending_queue.put(task_id)

                self.pending_queue.task_done()

            except queue.Empty:
                continue  # Timeout, check if we should keep running
            except Exception as e:
                logging.error(f"Error in worker loop: {e}")

    def get_running_task_count(self) -> int:
        with self.lock:
            return len(self.running_tasks)

    def get_pending_task_count(self) -> int:
        return self.pending_queue.qsize()

    def get_project_task_count(self, project_id: str) -> int:
        with self.lock:
            return self.project_task_counts.get(project_id, 0)

    def can_accept_new_task(self) -> bool:
        """Check if we can accept a new task (considering both running and queued)."""
        with self.lock:
            total_active = len(self.running_tasks) + self.pending_queue.qsize()
            return total_active < self.max_concurrent_tasks * 2  # Allow some queueing

    def add_task(self, task_info: TaskInfo) -> bool:
        """Add a task to the queue."""
        with self.lock:
            if not self.can_accept_new_task():
                return False

            # Store task metadata
            self.tasks[task_info.task_id] = task_info

            # Update project task count
            self.project_task_counts[task_info.project_id] = (
                self.project_task_counts.get(task_info.project_id, 0) + 1
            )

            # Add to pending queue
            self.pending_queue.put(task_info.task_id)

            logging.info(f"Task {task_info.task_id} added to queue")
            return True

    def complete_task(self, task_id: str, success: bool = True, error_message: str | None = None):
        """Mark a task as completed and clean up."""
        with self.lock:
            if task_id in self.tasks:
                task_info = self.tasks[task_id]
                task_info.status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
                task_info.completed_at = datetime.now()
                if error_message:
                    task_info.error_message = error_message

                # Remove from running tasks
                self.running_tasks.discard(task_id)

                # Decrement project task count
                if task_info.project_id in self.project_task_counts:
                    self.project_task_counts[task_info.project_id] -= 1
                    if self.project_task_counts[task_info.project_id] <= 0:
                        del self.project_task_counts[task_info.project_id]

                logging.info(f"Task {task_id} completed with status: {task_info.status}")

    def get_task(self, task_id: str) -> TaskInfo | None:
        with self.lock:
            return self.tasks.get(task_id)

    def get_all_tasks(self) -> list[TaskInfo]:
        with self.lock:
            return list(self.tasks.values())

    def get_project_tasks(self, project_id: str) -> list[TaskInfo]:
        with self.lock:
            return [task for task in self.tasks.values() if task.project_id == project_id]

    def store_task_data(self, task_id: str, data: any):
        """Store execution data for a task."""
        with self.lock:
            self.task_data[task_id] = data

    def get_task_data(self, task_id: str) -> any:
        """Retrieve execution data for a task."""
        with self.lock:
            return self.task_data.get(task_id)

    def _execute_task_sync(self, task_id: str, stdin_data):
        """Execute a task synchronously in a separate thread."""
        try:
            # Run the async task in a new event loop
            import asyncio

            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(run_rtl2gds_task(task_id, stdin_data))
            finally:
                loop.close()
        except Exception as e:
            logging.error(f"Error executing task {task_id}: {e}")
            self.complete_task(task_id, success=False, error_message=str(e))


# Global task manager instance
MAX_CONCURRENT_TASKS = int(os.getenv("MAX_CONCURRENT_TASKS", "5"))
task_manager = TaskManager(max_concurrent_tasks=MAX_CONCURRENT_TASKS)

# Start the worker thread when the module is loaded
task_manager.start_worker()

MOUNT_POINT = Path("/home/<USER>/RTL2GDS/tmp/projectData")

from enum import Enum


class TaskType(Enum):
    SYNTHESIS = StepName.SYNTHESIS
    FLOORPLAN = StepName.FLOORPLAN
    PLACEMENT = StepName.PLACEMENT
    CTS = StepName.CTS
    ROUTING = StepName.ROUTING
    SIGNOFF = StepName.SIGNOFF


# API Models
class StdinEDA(BaseModel):
    projectId: str
    taskId: str
    taskType: TaskType
    parameter: dict
    code: str
    userId: str
    ownerId: str


class ResponseData(BaseModel):
    code: str


class ResponseModel(BaseModel):
    code: int
    message: str
    data: ResponseData | None


class TaskSubmissionResponse(BaseModel):
    task_id: str
    status: str
    message: str


class TaskStatusResponse(BaseModel):
    task_id: str
    project_id: str
    step_name: str
    status: str
    created_at: datetime
    started_at: datetime | None = None
    completed_at: datetime | None = None
    error_message: str | None = None


class TaskListResponse(BaseModel):
    tasks: list[TaskStatusResponse]
    total_running: int
    max_concurrent: int


async def run_rtl2gds_task(task_id: str, stdin: StdinEDA) -> None:
    """
    Background task to run RTL2GDS cloud flow.

    Note: Task status is managed by the TaskManager worker thread.
    This function is called when the task is ready to execute.
    """
    try:
        logging.info(f"Executing RTL2GDS task {task_id} for project {stdin.projectId}")

        step_name = stdin.taskType
        proj_workspace = MOUNT_POINT / stdin.projectId
        cloud_config = stdin.parameter
        rtl_file = proj_workspace / "top.v"

        # Execute the RTL2GDS cloud main process
        python_executable = sys.executable
        cloud_main_path = Path(__file__).parent.parent.parent / "src" / "rtl2gds" / "cloud_main.py"

        cmd = [
            python_executable,
            str(cloud_main_path),
            str(rtl_file),
            str(cloud_config),
            str(proj_workspace),
            step_name,
        ]

        logging.info(f"Executing command: {' '.join(cmd)}")

        # Run the subprocess
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            env={**os.environ, "TASK_ID": task_id},
        )

        stdout, stderr = await process.communicate()

        if process.returncode == 0:
            logging.info(f"Task {task_id} completed successfully")
            logging.debug(f"Task {task_id} stdout: {stdout.decode()}")
            task_manager.complete_task(task_id, success=True)
        else:
            error_msg = (
                f"Process failed with return code {process.returncode}. stderr: {stderr.decode()}"
            )
            logging.error(f"Task {task_id} failed: {error_msg}")
            task_manager.complete_task(task_id, success=False, error_message=error_msg)

    except Exception as e:
        error_msg = f"Unexpected error in background task: {str(e)}"
        logging.exception(f"Task {task_id} failed with exception: {error_msg}")
        task_manager.complete_task(task_id, success=False, error_message=error_msg)


@app.post("/apis/v1/ieda/stdin", response_model=ResponseModel)
async def call_rtl2gds(stdin: StdinEDA) -> ResponseModel:
    """
    Handles requests to trigger RTL2GDS cloud flow steps with task queue management.
    """
    logging.info(f"Received request: {stdin}")

    try:
        step_name = stdin.taskType
        if step_name not in CLOUD_FLOW_STEPS:
            logging.warning(f"Invalid step name: {step_name}")
            raise HTTPException(status_code=400, detail=f"Invalid step name: {step_name}")

        # Check if we can start a new task
        if not task_manager.can_start_new_task():
            current_running = task_manager.get_running_task_count()
            raise HTTPException(
                status_code=429,
                detail=f"Task queue is full. Currently running {current_running}/{task_manager.max_concurrent_tasks} tasks. Please try again later.",
            )

        # /projectData/project_<projectId>
        proj_workspace = MOUNT_POINT / f"project_{stdin.projectId}"
        if not proj_workspace.is_dir():
            logging.error(f"Eda workspace {proj_workspace} not found or is not a directory.")
            raise HTTPException(status_code=404, detail="Not Found: EDA workspace does not exist")

        # Create task
        task_info = TaskInfo(
            task_id=stdin.taskId,
            project_id=stdin.projectId,
            step_name=step_name,
            status=TaskStatus.QUEUED,
            created_at=datetime.now(),
        )

        # Add task to queue
        if not task_manager.add_task(task_info):
            current_running = task_manager.get_running_task_count()
            raise HTTPException(
                status_code=429,
                detail=f"Failed to add task to queue. Currently running {current_running}/{task_manager.max_concurrent_tasks} tasks.",
            )

        # Store the stdin data for later execution
        task_manager.store_task_data(stdin.taskId, stdin)

        logging.info(f"Task {stdin.taskId} queued successfully for project {stdin.projectId}")

        return ResponseModel(
            code=0,
            message=f"Task queued successfully. Task ID: {stdin.taskId}",
            data=ResponseData(code="TASK_QUEUED"),
        )

    except HTTPException:
        raise
    except Exception as e:
        logging.exception(f"An unexpected error occurred in call_rtl2gds: {e}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: Unexpected server error: {str(e)}"
        )


# New API endpoints for task management
@app.get("/apis/v1/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str) -> TaskStatusResponse:
    """
    Get the status of a specific task.
    """
    task_info = task_manager.get_task(task_id)
    if not task_info:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

    return TaskStatusResponse(
        task_id=task_info.task_id,
        project_id=task_info.project_id,
        step_name=task_info.step_name,
        status=task_info.status,
        created_at=task_info.created_at,
        started_at=task_info.started_at,
        completed_at=task_info.completed_at,
        error_message=task_info.error_message,
    )


@app.get("/apis/v1/tasks", response_model=TaskListResponse)
async def get_all_tasks() -> TaskListResponse:
    """
    Get the status of all tasks.
    """
    all_tasks = task_manager.get_all_tasks()
    task_responses = [
        TaskStatusResponse(
            task_id=task.task_id,
            project_id=task.project_id,
            step_name=task.step_name,
            status=task.status,
            created_at=task.created_at,
            started_at=task.started_at,
            completed_at=task.completed_at,
            error_message=task.error_message,
        )
        for task in all_tasks
    ]

    return TaskListResponse(
        tasks=task_responses,
        total_running=task_manager.get_running_task_count(),
        max_concurrent=task_manager.max_concurrent_tasks,
    )


@app.get("/apis/v1/projects/{project_id}/tasks", response_model=TaskListResponse)
async def get_project_tasks(project_id: str) -> TaskListResponse:
    """
    Get the status of all tasks for a specific project.
    """
    project_tasks = task_manager.get_project_tasks(project_id)
    task_responses = [
        TaskStatusResponse(
            task_id=task.task_id,
            project_id=task.project_id,
            step_name=task.step_name,
            status=task.status,
            created_at=task.created_at,
            started_at=task.started_at,
            completed_at=task.completed_at,
            error_message=task.error_message,
        )
        for task in project_tasks
    ]

    return TaskListResponse(
        tasks=task_responses,
        total_running=task_manager.get_running_task_count(),
        max_concurrent=task_manager.max_concurrent_tasks,
    )


@app.get("/apis/v1/queue/status")
async def get_queue_status():
    """
    Get current queue status and statistics.
    """
    return {
        "total_running": task_manager.get_running_task_count(),
        "total_pending": task_manager.get_pending_task_count(),
        "max_concurrent": task_manager.max_concurrent_tasks,
        "available_slots": task_manager.max_concurrent_tasks
        - task_manager.get_running_task_count(),
        "project_task_counts": dict(task_manager.project_task_counts),
    }


def get_expected_step(finished_step: str) -> str | None:
    try:
        index = CLOUD_FLOW_STEPS.index(finished_step)
        if index == len(CLOUD_FLOW_STEPS) - 1:
            return None
        return CLOUD_FLOW_STEPS[index + 1]
    except ValueError:
        return None


@app.get("/hello")
def get_hello():
    return {"Hello": "RTL2GDS Cloud YES!"}


if __name__ == "__main__":
    eda_service_host = os.getenv("EDA_SERVICE_HOST", "************")
    eda_service_port = 9444
    log_level = os.getenv("LOG_LEVEL", "info").lower()
    print(f"Starting RTL2GDS API server on {eda_service_host}:{eda_service_port}")
    uvicorn.run(
        app="cloud:app",
        host=eda_service_host,
        port=eda_service_port,
        log_level=log_level,
        reload=False,
    )
