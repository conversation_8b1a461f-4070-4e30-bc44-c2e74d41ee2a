#!/usr/bin/env python3
"""
Test script for the RTL2GDS Cloud API with task queue management.
"""

import asyncio
import json
import time
from pathlib import Path

try:
    import httpx
    import pytest

    HAS_HTTPX = True
except ImportError:
    HAS_HTTPX = False


class TestCloudAPI:
    """Test cases for the RTL2GDS Cloud API."""

    BASE_URL = "http://localhost:9444"

    def setup_method(self):
        """Setup test environment."""
        self.client = httpx.AsyncClient(base_url=self.BASE_URL)

    def teardown_method(self):
        """Cleanup test environment."""
        asyncio.run(self.client.aclose())

    async def test_hello_endpoint(self):
        """Test the hello endpoint."""
        async with httpx.AsyncClient(base_url=self.BASE_URL) as client:
            response = await client.get("/hello")
            assert response.status_code == 200
            assert response.json() == {"Hello": "RTL2GDS Cloud YES!"}

    async def test_queue_status_endpoint(self):
        """Test the queue status endpoint."""
        async with httpx.AsyncClient(base_url=self.BASE_URL) as client:
            response = await client.get("/apis/v1/queue/status")
            assert response.status_code == 200
            data = response.json()
            assert "total_running" in data
            assert "max_concurrent" in data
            assert "available_slots" in data
            assert "project_task_counts" in data

    async def test_get_all_tasks_empty(self):
        """Test getting all tasks when queue is empty."""
        async with httpx.AsyncClient(base_url=self.BASE_URL) as client:
            response = await client.get("/apis/v1/tasks")
            assert response.status_code == 200
            data = response.json()
            assert "tasks" in data
            assert "total_running" in data
            assert "max_concurrent" in data

    async def test_get_nonexistent_task(self):
        """Test getting a task that doesn't exist."""
        async with httpx.AsyncClient(base_url=self.BASE_URL) as client:
            response = await client.get("/apis/v1/tasks/nonexistent-task-id")
            assert response.status_code == 404

    async def test_submit_task_invalid_step(self):
        """Test submitting a task with invalid step name."""
        async with httpx.AsyncClient(base_url=self.BASE_URL) as client:
            payload = {
                "code": "invalid_step",
                "ownerId": "test_owner",
                "projectID": "test_project",
                "userId": "test_user",
                "filePath": None,
                "parameters": {},
            }
            response = await client.post("/apis/v1/ieda/stdin", json=payload)
            assert response.status_code == 400

    async def test_submit_task_missing_workspace(self):
        """Test submitting a task with missing workspace."""
        async with httpx.AsyncClient(base_url=self.BASE_URL) as client:
            payload = {
                "code": "synthesis",
                "ownerId": "test_owner",
                "projectID": "nonexistent_project",
                "userId": "test_user",
                "filePath": None,
                "parameters": {},
            }
            response = await client.post("/apis/v1/ieda/stdin", json=payload)
            # This will fail because /projectData doesn't exist in test environment
            assert response.status_code in [404, 503]


def test_task_manager_functionality():
    """Test TaskManager class functionality."""
    import sys
    from datetime import datetime
    from pathlib import Path

    # Add the current directory to Python path
    current_dir = Path(__file__).parent.parent.parent
    sys.path.insert(0, str(current_dir))

    from design_zoo.gcd_cloud.cloud import TaskInfo, TaskManager, TaskStatus

    # Create task manager with max 2 concurrent tasks
    manager = TaskManager(max_concurrent_tasks=2)

    # Test initial state
    assert manager.get_running_task_count() == 0
    assert manager.can_start_new_task() == True
    assert manager.get_project_task_count("project1") == 0

    # Add first task
    task1 = TaskInfo(
        task_id="task1",
        project_id="project1",
        step_name="synthesis",
        status=TaskStatus.QUEUED,
        created_at=datetime.now(),
    )
    assert manager.add_task(task1) == True
    assert manager.get_project_task_count("project1") == 1

    # Start the task
    manager.start_task("task1")
    assert manager.get_running_task_count() == 1
    assert manager.can_start_new_task() == True

    # Add second task
    task2 = TaskInfo(
        task_id="task2",
        project_id="project2",
        step_name="floorplan",
        status=TaskStatus.QUEUED,
        created_at=datetime.now(),
    )
    assert manager.add_task(task2) == True
    manager.start_task("task2")
    assert manager.get_running_task_count() == 2
    assert manager.can_start_new_task() == False

    # Try to add third task (should fail)
    task3 = TaskInfo(
        task_id="task3",
        project_id="project1",
        step_name="placement",
        status=TaskStatus.QUEUED,
        created_at=datetime.now(),
    )
    assert manager.add_task(task3) == False

    # Complete first task
    manager.complete_task("task1", success=True)
    assert manager.get_running_task_count() == 1
    assert manager.can_start_new_task() == True
    assert manager.get_project_task_count("project1") == 0

    # Get task info
    retrieved_task = manager.get_task("task2")
    assert retrieved_task is not None
    assert retrieved_task.task_id == "task2"
    assert retrieved_task.status == TaskStatus.RUNNING

    # Complete second task with error
    manager.complete_task("task2", success=False, error_message="Test error")
    retrieved_task = manager.get_task("task2")
    assert retrieved_task.status == TaskStatus.FAILED
    assert retrieved_task.error_message == "Test error"
    assert manager.get_running_task_count() == 0


if __name__ == "__main__":
    # Run the task manager test
    test_task_manager_functionality()
    print("✅ TaskManager functionality tests passed!")

    if not HAS_HTTPX:
        print("⚠️  httpx not available, skipping API tests")
        print("📝 To run API tests:")
        print("1. Install httpx: pip install httpx pytest")
        print("2. Start the server: python design_zoo/gcd_cloud/cloud.py")
        print("3. Run tests: pytest design_zoo/gcd_cloud/test_cloud_api.py")
    else:
        print("📝 To run API tests:")
        print("1. Start the server: python design_zoo/gcd_cloud/cloud.py")
        print("2. Run tests: pytest design_zoo/gcd_cloud/test_cloud_api.py")
